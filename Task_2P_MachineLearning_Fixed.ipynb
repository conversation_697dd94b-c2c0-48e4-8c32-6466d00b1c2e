# Import required libraries for data analysis and HTTP requests
import pandas as pd      # Data manipulation and analysis
import numpy as np       # Numerical operations
import requests          # For downloading data from the web
from io import StringIO  # For reading CSV data from string
# Import additional libraries for preprocessing and visualization
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler, StandardScaler
from sklearn.preprocessing import LabelEncoder, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from sklearn.metrics import adjusted_rand_score, normalized_mutual_info_score
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette('husl')

print('Numpy Version',np.__version__)
print('Pandas Version',pd.__version__)
print('Seaborn Version',sns.__version__)

def load_data(local_file_path=None, url=None, delimiter=','):
    """
    Load dataset from local file or remote URL with fallback logic
    
    Parameters:
    -----------
    local_file_path : str, optional
        Path to local CSV file. If provided, will try to load from this file first.
    url : str, optional
        URL to download data from if local file fails or is not provided.
    
    Returns:
    --------
    pandas.DataFrame or None
        Returns the loaded DataFrame or None if both local and remote loading fail
    """
    import os
    
    # Try loading from local file first if path is provided
    if local_file_path:
        print(f"Attempting to load dataset from local file: {local_file_path}")
        try:
            if not os.path.exists(local_file_path):
                print(f"Local file not found: {local_file_path}")
                if url:
                    print("Falling back to downloading from URL...")
                else:
                    return None
            else:
                df = pd.read_csv(local_file_path, delimiter=delimiter)
                print(f"Dataset loaded successfully from local file! Shape: {df.shape}")
                return df
                
        except Exception as e:
            print(f"Error reading local file: {e}")
            if url:
                print("Falling back to downloading from URL...")
            else:
                return None
    
    # Load from URL (either as primary method or fallback)
    if url:
        print(f"Loading dataset from URL...")
        
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            
            # Read CSV data from the response
            df = pd.read_csv(StringIO(response.text),delimiter=delimiter)
            print(f"Dataset loaded successfully from URL! Shape: {df.shape}")
            return df
            
        except Exception as e:
            print(f"Error loading from URL: {e}")
            return None
    
    print("No data source provided")
    return None

# Using the load_data function with semicolon delimiter
# First try with the load_data function
bank_df = load_data(
    local_file_path='C:/datasets/2P/bank-additional.csv',
    delimiter=';'
)

# If load_data fails, try direct loading with semicolon delimiter
if bank_df is None:
    try:
        bank_df = pd.read_csv('C:/datasets/2P/bank-additional.csv', delimiter=';')
        print(f"Dataset loaded directly! Shape: {bank_df.shape}")
    except Exception as e:
        print(f"Error loading dataset: {e}")

# Validate that data was loaded correctly
if bank_df is not None:
    print(f"\nData validation:")
    print(f"Dataset shape: {bank_df.shape}")
    print(f"Number of columns: {len(bank_df.columns)}")
    print(f"Column names: {list(bank_df.columns)}")
    print(f"Number of missing values: {bank_df.isnull().sum().sum()}")

# Check if data loaded correctly
if bank_df is not None:
    print("Dataset Info:")
    bank_df.info()
    print(f"\nDataset shape: {bank_df.shape}")
    print(f"\nFirst few rows:")
    print(bank_df.head())
else:
    print("Failed to load dataset. Please check the file path and format.")

# Check if data loaded correctly

if bank_df is not None:
    print(f"\nFirst few rows:")
else:
    print("Failed to load dataset. Please check the file path and format.")

bank_df.head()

# Data Exploration and Preprocessing
print("=" * 60)
print("BANK MARKETING DATASET - DATA EXPLORATION")
print("=" * 60)

if bank_df is not None:
    # Basic dataset information
    print(f"Dataset shape: {bank_df.shape}")
    print(f"\nColumn names: {list(bank_df.columns)}")
    
    # Check target variable distribution
    print(f"\nTarget variable (y) distribution:")
    print(bank_df['y'].value_counts())
    print(f"\nTarget variable proportions:")
    print(bank_df['y'].value_counts(normalize=True))
    
    # Check for missing values
    print(f"\nMissing values per column:")
    missing_values = bank_df.isnull().sum()
    print(missing_values[missing_values > 0])
    
    # Check for 'unknown' values (coded missing values)
    print(f"\n'Unknown' values per column:")
    for col in bank_df.columns:
        if bank_df[col].dtype == 'object':
            unknown_count = bank_df[col].str.lower().eq('unknown').sum()
            if unknown_count > 0:
                print(f"{col}: {unknown_count} unknown values ({unknown_count/len(bank_df)*100:.2f}%)")

def determine_imputation_strategy(df, feature):
    """
    Determine whether to use mean or median for imputation based on data distribution
    """
    # Use mode for categorical features
    if df[feature].dtype in ['object', 'category']:
        return 'mode'
    # For numerical features, check skewness
    skewness = df[feature].skew()
    # Use median if highly skewed, mean if approximately normal
    if abs(skewness) > 1:
        return 'median'
    else:
        return 'mean'



def fill_missing_values(df):
    """
    Fill missing values using appropriate imputation strategy for each feature
    """
    print("=" * 60)
    print("MISSING VALUES IMPUTATION")
    print("=" * 60)
    
    df_filled = df.copy()
    imputation_summary = {}
    
    # Find features with missing values
    missing_counts = df.isnull().sum()
    features_with_missing = missing_counts[missing_counts > 0].index
    
    if len(features_with_missing) == 0:
        print("No missing values found in the dataset!")
        return df_filled, imputation_summary
    
    # Impute missing values for each feature
    for feature in features_with_missing:
        print(f'Processing feature: {feature}')
        
        if df[feature].dtype in ['object', 'category']:
            # Use mode for categorical features
            mode_value = df[feature].mode().iloc[0] if not df[feature].mode().empty else 'unknown'
            df_filled[feature].fillna(mode_value, inplace=True)
            imputation_summary[feature] = {
                'strategy': 'mode',
                'replacement_value': mode_value,
                'reason': 'Categorical feature - using most frequent value'
            }
            print(f'  Strategy: Mode')
            print(f'  Replacement value: {mode_value}')
        else:
            # Use mean or median for numerical features based on skewness
            strategy = determine_imputation_strategy(df, feature)
            if strategy == 'mean':
                replacement_value = df[feature].mean()
                reason = f'Numerical feature with low skewness ({df[feature].skew():.3f}) - using mean'
            else:
                replacement_value = df[feature].median()
                reason = f'Numerical feature with high skewness ({df[feature].skew():.3f}) - using median'
            
            df_filled[feature].fillna(replacement_value, inplace=True)
            imputation_summary[feature] = {
                'strategy': strategy,
                'replacement_value': replacement_value,
                'reason': reason
            }
            print(f'  Strategy: {strategy.capitalize()}')
            print(f'  Replacement value: {replacement_value:.6f}')
        print()
    
    return df_filled, imputation_summary

# Apply missing values imputation to the bank dataset
if bank_df is not None:
    # First, handle 'unknown' values by converting them to NaN for proper imputation
    print("Converting 'unknown' values to NaN for proper imputation...")
    bank_df_for_imputation = bank_df.copy()
    
    # Replace 'unknown' with NaN in categorical columns
    categorical_cols = bank_df_for_imputation.select_dtypes(include=['object']).columns
    for col in categorical_cols:
        if col != 'y':  # Don't modify the target variable
            unknown_count = (bank_df_for_imputation[col] == 'unknown').sum()
            if unknown_count > 0:
                print(f"Converting {unknown_count} 'unknown' values to NaN in column '{col}'")
                bank_df_for_imputation[col] = bank_df_for_imputation[col].replace('unknown', np.nan)
    
    # Apply imputation
    bank_df_imputed, imputation_summary = fill_missing_values(bank_df_for_imputation)
    
   

#convert imputation_summary to a dataframe
imputation_summary_df = pd.DataFrame.from_dict(imputation_summary, orient='index')
imputation_summary_df


# Display imputation summary
print("\nImputation Summary:")
print("=" * 50)
for feature, details in imputation_summary.items():
    print(f"{feature}:")
    print(f"  Strategy: {details['strategy']}")
    print(f"  Replacement: {details['replacement_value']}")
    print(f"  Reason: {details['reason']}")
    print()

print(f"\nDataset shape after imputation: {bank_df_imputed.shape}")
print(f"Missing values after imputation: {bank_df_imputed.isnull().sum().sum()}")

# Update the main dataframe
bank_df = bank_df_imputed
print("\nMissing values imputation completed successfully!")




# Data Preprocessing Function
def preprocess_bank_data(df, handle_duration=True):
    """
    Preprocess the bank marketing dataset
    
    Parameters:
    -----------
    df : pandas.DataFrame
        Raw bank marketing dataset
    handle_duration : bool
        Whether to exclude duration column (recommended for realistic prediction)
    
    Returns:
    --------
    X : pandas.DataFrame
        Preprocessed features
    y : pandas.Series
        Target variable (binary encoded)
    """
    from sklearn.preprocessing import LabelEncoder, StandardScaler
    from sklearn.compose import ColumnTransformer
    from sklearn.preprocessing import OneHotEncoder
    
    df_processed = df.copy()
    
    # Remove duration column if specified (recommended for realistic prediction)
    if handle_duration and 'duration' in df_processed.columns:
        print("Removing 'duration' column for realistic prediction model")
        df_processed = df_processed.drop('duration', axis=1)
    
    # Separate features and target
    X = df_processed.drop('y', axis=1)
    y = df_processed['y']
    
    # Encode target variable (yes=1, no=0)
    le_target = LabelEncoder()
    y_encoded = le_target.fit_transform(y)
    
    # Identify categorical and numerical columns
    categorical_features = X.select_dtypes(include=['object']).columns.tolist()
    numerical_features = X.select_dtypes(include=[np.number]).columns.tolist()
    
    print(f"Categorical features: {categorical_features}")
    print(f"Numerical features: {numerical_features}")
    
    # Create preprocessing pipeline
    preprocessor = ColumnTransformer(
        transformers=[
            ('num', StandardScaler(), numerical_features),
            ('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), categorical_features)
        ]
    )
    
    # Fit and transform the data
    X_processed = preprocessor.fit_transform(X)
    
    # Debug information
    print(f'Original data shape: {df.shape}')
    print(f'Features shape before preprocessing: {X.shape}')
    print(f'Processed data shape: {X_processed.shape}')
    print(f'Number of numerical features: {len(numerical_features)}')
    print(f'Number of categorical features: {len(categorical_features)}')
    
    # Check if data was loaded correctly
    if X_processed.shape[1] == 1 and len(numerical_features) + len(categorical_features) > 1:
        print('ERROR: Data appears to be loaded incorrectly!')
        print('This usually means the CSV delimiter is wrong.')
        print('Please check that the data is loaded with delimiter=";"')
        print('Current data columns:', list(df.columns))
        return None, None, None, None
    
    # Get feature names after preprocessing - handle potential issues
    try:
        # Get numerical feature names
        num_feature_names = numerical_features
        
        # Get categorical feature names after one-hot encoding
        cat_feature_names = list(preprocessor.named_transformers_['cat'].get_feature_names_out(categorical_features))
        
        # Combine all feature names
        feature_names = num_feature_names + cat_feature_names
        
        # Verify the number of features matches
        if len(feature_names) != X_processed.shape[1]:
            print(f'Warning: Feature name count ({len(feature_names)}) does not match processed data columns ({X_processed.shape[1]})')
            # Create generic feature names as fallback
            feature_names = [f'feature_{i}' for i in range(X_processed.shape[1])]
        
        # Convert to DataFrame for easier handling
        X_processed_df = pd.DataFrame(X_processed, columns=feature_names)
        
    except Exception as e:
        print(f'Error creating feature names: {e}')
        # Fallback: create generic column names
        feature_names = [f'feature_{i}' for i in range(X_processed.shape[1])]
        X_processed_df = pd.DataFrame(X_processed, columns=feature_names)
    
    print(f"\nPreprocessed dataset shape: {X_processed_df.shape}")
    print(f"Target encoding: {dict(zip(le_target.classes_, le_target.transform(le_target.classes_)))}")
    
    return X_processed_df, y_encoded, preprocessor, le_target

# Apply preprocessing
if bank_df is not None:
    X, y, encoding_info = preprocess_bank_data(bank_df)
    if X is not None:
        print("\nPreprocessing completed successfully!")
        print(f"\nEncoding Summary:")
        print(f"Binary features (Label Encoded): {encoding_info['binary_features']}")
        print(f"Multi-class features (One-Hot Encoded): {encoding_info['multiclass_features']}")
        print(f"Numerical features (Scaled): {encoding_info['numerical_features']}")
    else:
        print("\nPreprocessing failed!")
else:
    print("Cannot preprocess data - dataset not loaded")

# Train-Test Split and Model Training
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
from sklearn.metrics import classification_report, confusion_matrix
import time

if 'X' in locals() and 'y' in locals():
    # Split the data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"Training set shape: {X_train.shape}")
    print(f"Test set shape: {X_test.shape}")
    print(f"Training set target distribution: {np.bincount(y_train)}")
    print(f"Test set target distribution: {np.bincount(y_test)}")
else:
    print("Data not available for splitting")

# Model Evaluation Function
def evaluate_model(model, X_test, y_test, model_name="Model"):
    """
    Evaluate a trained model and return comprehensive metrics
    """
    # Make predictions
    y_pred = model.predict(X_test)
    
    # Calculate metrics
    accuracy = accuracy_score(y_test, y_pred)
    precision = precision_score(y_test, y_pred)
    recall = recall_score(y_test, y_pred)
    f1 = f1_score(y_test, y_pred)
    
    # Print results
    print(f"\n{model_name} Performance:")
    print("-" * 40)
    print(f"Accuracy:  {accuracy:.4f}")
    print(f"Precision: {precision:.4f}")
    print(f"Recall:    {recall:.4f}")
    print(f"F1-Score:  {f1:.4f}")
    
    # Confusion Matrix
    cm = confusion_matrix(y_test, y_pred)
    print(f"\nConfusion Matrix:")
    print(cm)
    
    return {
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'f1_score': f1,
        'confusion_matrix': cm
    }

# 1. Standard Logistic Regression Model
print("=" * 60)
print("1. STANDARD LOGISTIC REGRESSION")
print("=" * 60)

if 'X_train' in locals():
    # Train standard logistic regression
    start_time = time.time()
    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train, y_train)
    lr_training_time = time.time() - start_time
    
    print(f"Training time: {lr_training_time:.4f} seconds")
    print(f"Number of features: {X_train.shape[1]}")
    print(f"Number of trainable parameters: {X_train.shape[1] + 1}")  # weights + bias
    
    # Evaluate the model
    lr_results = evaluate_model(lr_model, X_test, y_test, "Standard Logistic Regression")
    
    # Store results for comparison
    lr_results['training_time'] = lr_training_time
    lr_results['n_parameters'] = X_train.shape[1] + 1
else:
    print("Training data not available")

# 2. Regularized Logistic Regression Models
print("\n" + "=" * 60)
print("2. REGULARIZED LOGISTIC REGRESSION MODELS")
print("=" * 60)

if 'X_train' in locals():
    # L1 Regularization (Lasso)
    print("\n2a. L1 Regularized Logistic Regression (Lasso)")
    print("-" * 50)
    
    start_time = time.time()
    lr_l1 = LogisticRegression(penalty='l1', solver='liblinear', C=1.0, random_state=42, max_iter=1000)
    lr_l1.fit(X_train, y_train)
    lr_l1_training_time = time.time() - start_time
    
    print(f"Training time: {lr_l1_training_time:.4f} seconds")
    print(f"Regularization parameter (C): {lr_l1.C}")
    print(f"Number of non-zero coefficients: {np.sum(lr_l1.coef_ != 0)}")
    
    lr_l1_results = evaluate_model(lr_l1, X_test, y_test, "L1 Regularized Logistic Regression")
    lr_l1_results['training_time'] = lr_l1_training_time
    lr_l1_results['n_parameters'] = X_train.shape[1] + 1
    lr_l1_results['non_zero_coef'] = np.sum(lr_l1.coef_ != 0)
    
    # L2 Regularization (Ridge)
    print("\n2b. L2 Regularized Logistic Regression (Ridge)")
    print("-" * 50)
    
    start_time = time.time()
    lr_l2 = LogisticRegression(penalty='l2', C=1.0, random_state=42, max_iter=1000)
    lr_l2.fit(X_train, y_train)
    lr_l2_training_time = time.time() - start_time
    
    print(f"Training time: {lr_l2_training_time:.4f} seconds")
    print(f"Regularization parameter (C): {lr_l2.C}")
    
    lr_l2_results = evaluate_model(lr_l2, X_test, y_test, "L2 Regularized Logistic Regression")
    lr_l2_results['training_time'] = lr_l2_training_time
    lr_l2_results['n_parameters'] = X_train.shape[1] + 1
else:
    print("Training data not available")

# 3. K-Nearest Neighbors (KNN) Baseline Model
print("\n" + "=" * 60)
print("3. K-NEAREST NEIGHBORS (KNN) BASELINE")
print("=" * 60)

if 'X_train' in locals():
    # Try different k values to find optimal
    k_values = [3, 5, 7, 9, 11]
    knn_results = {}
    
    print("Testing different k values...")
    for k in k_values:
        start_time = time.time()
        knn = KNeighborsClassifier(n_neighbors=k)
        knn.fit(X_train, y_train)
        training_time = time.time() - start_time
        
        # Quick evaluation
        y_pred = knn.predict(X_test)
        accuracy = accuracy_score(y_test, y_pred)
        
        knn_results[k] = {
            'model': knn,
            'accuracy': accuracy,
            'training_time': training_time
        }
        
        print(f"k={k}: Accuracy={accuracy:.4f}, Training time={training_time:.4f}s")
    
    # Select best k based on accuracy
    best_k = max(knn_results.keys(), key=lambda k: knn_results[k]['accuracy'])
    best_knn = knn_results[best_k]['model']
    
    print(f"\nBest k value: {best_k}")
    print(f"Number of trainable parameters: 0 (non-parametric model)")
    print(f"Training data storage: {X_train.shape[0]} samples × {X_train.shape[1]} features")
    
    # Detailed evaluation of best KNN
    knn_detailed_results = evaluate_model(best_knn, X_test, y_test, f"KNN (k={best_k})")
    knn_detailed_results['training_time'] = knn_results[best_k]['training_time']
    knn_detailed_results['n_parameters'] = 0  # Non-parametric
    knn_detailed_results['k_value'] = best_k
    knn_detailed_results['storage_requirement'] = X_train.shape[0] * X_train.shape[1]
else:
    print("Training data not available")

# 4. Comprehensive Model Comparison
print("\n" + "=" * 80)
print("4. COMPREHENSIVE MODEL COMPARISON")
print("=" * 80)

if all(var in locals() for var in ['lr_results', 'lr_l1_results', 'lr_l2_results', 'knn_detailed_results']):
    
    # Create comparison DataFrame
    comparison_data = {
        'Model': [
            'Standard Logistic Regression',
            'L1 Regularized Logistic Regression',
            'L2 Regularized Logistic Regression',
            f'KNN (k={best_k})'
        ],
        'Accuracy': [
            lr_results['accuracy'],
            lr_l1_results['accuracy'],
            lr_l2_results['accuracy'],
            knn_detailed_results['accuracy']
        ],
        'Precision': [
            lr_results['precision'],
            lr_l1_results['precision'],
            lr_l2_results['precision'],
            knn_detailed_results['precision']
        ],
        'Recall': [
            lr_results['recall'],
            lr_l1_results['recall'],
            lr_l2_results['recall'],
            knn_detailed_results['recall']
        ],
        'F1-Score': [
            lr_results['f1_score'],
            lr_l1_results['f1_score'],
            lr_l2_results['f1_score'],
            knn_detailed_results['f1_score']
        ],
        'Training Time (s)': [
            lr_results['training_time'],
            lr_l1_results['training_time'],
            lr_l2_results['training_time'],
            knn_detailed_results['training_time']
        ],
        'Parameters': [
            lr_results['n_parameters'],
            lr_l1_results['n_parameters'],
            lr_l2_results['n_parameters'],
            knn_detailed_results['n_parameters']
        ]
    }
    
    comparison_df = pd.DataFrame(comparison_data)
    
    print("Performance Comparison:")
    print(comparison_df.round(4))
    
    # Find best performing model for each metric
    print("\nBest performing models by metric:")
    print("-" * 40)
    for metric in ['Accuracy', 'Precision', 'Recall', 'F1-Score']:
        best_idx = comparison_df[metric].idxmax()
        best_model = comparison_df.loc[best_idx, 'Model']
        best_score = comparison_df.loc[best_idx, metric]
        print(f"{metric}: {best_model} ({best_score:.4f})")
    
    # Training efficiency
    fastest_idx = comparison_df['Training Time (s)'].idxmin()
    fastest_model = comparison_df.loc[fastest_idx, 'Model']
    fastest_time = comparison_df.loc[fastest_idx, 'Training Time (s)']
    print(f"Fastest Training: {fastest_model} ({fastest_time:.4f}s)")
    
else:
    print("Not all models have been trained yet")

# 5. Analysis and Explanation
print("\n" + "=" * 80)
print("5. ANALYSIS: KNN vs LOGISTIC REGRESSION")
print("=" * 80)

print("""
COMPARISON ANALYSIS:

1. NUMBER OF TRAINABLE PARAMETERS:
   • Logistic Regression: {} parameters (weights + bias)
     - Each feature gets a weight coefficient
     - One bias term
     - Parameters are learned during training
   
   • KNN: 0 trainable parameters
     - Non-parametric algorithm
     - No model parameters to learn
     - Stores entire training dataset instead

2. TRAINING TIME:
   • Logistic Regression: Moderate training time
     - Iterative optimization algorithm
     - Convergence depends on data complexity
     - One-time training cost
   
   • KNN: Very fast "training"
     - Simply stores the training data
     - No actual learning/optimization
     - Lazy learning approach

3. PREDICTION TIME (not measured but important):
   • Logistic Regression: Very fast prediction
     - Simple linear combination + sigmoid
     - O(n_features) complexity
   
   • KNN: Slow prediction
     - Must compute distance to all training points
     - O(n_training_samples × n_features) complexity
     - Becomes slower as training data grows

4. MEMORY REQUIREMENTS:
   • Logistic Regression: Low memory
     - Only stores learned parameters
     - Memory usage independent of training set size
   
   • KNN: High memory
     - Must store entire training dataset
     - Memory grows linearly with training data

5. MODEL INTERPRETABILITY:
   • Logistic Regression: Highly interpretable
     - Coefficients show feature importance
     - Can understand decision boundaries
   
   • KNN: Less interpretable
     - Decisions based on local neighborhoods
     - Harder to understand global patterns

6. PERFORMANCE CHARACTERISTICS:
   • Logistic Regression: 
     - Works well with linear relationships
     - Assumes linear decision boundary
     - Regularization helps prevent overfitting
   
   • KNN:
     - Can capture non-linear relationships
     - Flexible decision boundaries
     - Sensitive to curse of dimensionality
     - Sensitive to irrelevant features
""".format('N/A' if 'X_train' not in locals() else X_train.shape[1] + 1))

if 'comparison_df' in locals():
    lr_accuracy = comparison_df[comparison_df['Model'] == 'Standard Logistic Regression']['Accuracy'].iloc[0]
    knn_accuracy = comparison_df[comparison_df['Model'].str.contains('KNN')]['Accuracy'].iloc[0]
    
    if lr_accuracy > knn_accuracy:
        print(f"""
CONCLUSION:
Logistic Regression outperforms KNN in this case because:
• The bank marketing dataset likely has linear relationships
• High dimensionality after one-hot encoding hurts KNN
• KNN suffers from curse of dimensionality
• Logistic regression is more robust to irrelevant features
• The dataset size is large enough that local patterns may not generalize well
        """)
    else:
        print(f"""
CONCLUSION:
KNN outperforms Logistic Regression in this case because:
• The relationships in the data may be non-linear
• Local patterns are more informative than global linear trends
• The feature space allows for meaningful distance calculations
• The optimal k value captures the right level of locality
        """)

# 6. Visualization of Results
print("\n" + "=" * 60)
print("6. RESULTS VISUALIZATION")
print("=" * 60)

if 'comparison_df' in locals():
    # Create subplots for different metrics
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Model Performance Comparison', fontsize=16, fontweight='bold')
    
    # Performance metrics
    metrics = ['Accuracy', 'Precision', 'Recall', 'F1-Score']
    colors = ['skyblue', 'lightcoral', 'lightgreen', 'gold']
    
    for i, (metric, color) in enumerate(zip(metrics, colors)):
        ax = axes[i//2, i%2]
        bars = ax.bar(range(len(comparison_df)), comparison_df[metric], color=color, alpha=0.7)
        ax.set_title(f'{metric} Comparison', fontweight='bold')
        ax.set_ylabel(metric)
        ax.set_xticks(range(len(comparison_df)))
        ax.set_xticklabels([name.replace(' ', '\n') for name in comparison_df['Model']], 
                          rotation=0, ha='center')
        ax.grid(axis='y', alpha=0.3)
        
        # Add value labels on bars
        for bar, value in zip(bars, comparison_df[metric]):
            ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                   f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
    # Training time comparison
    plt.figure(figsize=(12, 6))
    bars = plt.bar(range(len(comparison_df)), comparison_df['Training Time (s)'], 
                   color='mediumpurple', alpha=0.7)
    plt.title('Training Time Comparison', fontsize=14, fontweight='bold')
    plt.ylabel('Training Time (seconds)')
    plt.xticks(range(len(comparison_df)), 
               [name.replace(' ', '\n') for name in comparison_df['Model']])
    plt.grid(axis='y', alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars, comparison_df['Training Time (s)']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(comparison_df['Training Time (s)'])*0.01,
                f'{value:.4f}s', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
    # Model complexity comparison
    plt.figure(figsize=(10, 6))
    bars = plt.bar(range(len(comparison_df)), comparison_df['Parameters'], 
                   color='orange', alpha=0.7)
    plt.title('Model Complexity (Number of Parameters)', fontsize=14, fontweight='bold')
    plt.ylabel('Number of Parameters')
    plt.xticks(range(len(comparison_df)), 
               [name.replace(' ', '\n') for name in comparison_df['Model']])
    plt.grid(axis='y', alpha=0.3)
    
    # Add value labels
    for bar, value in zip(bars, comparison_df['Parameters']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(comparison_df['Parameters'])*0.01,
                f'{int(value)}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.show()
    
else:
    print("Comparison data not available for visualization")