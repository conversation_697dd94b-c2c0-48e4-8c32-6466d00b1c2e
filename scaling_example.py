import pandas as pd
import numpy as np

# Example showing why we need to scale encoded categorical features
print("=" * 60)
print("WHY SCALE ENCODED CATEGORICAL FEATURES")
print("=" * 60)

# Sample data after encoding
data = {
    'Age': [25, 45, 35, 55],           # Numerical: 25-55 range
    'Weight': [60, 80, 70, 90],        # Numerical: 60-90 range  
    'Gender': [0, 1, 0, 1],            # Label encoded: 0-1 range
    'CALC_Sometimes': [1, 0, 1, 0],    # One-hot: 0-1 range
    'CALC_Frequently': [0, 1, 0, 0]    # One-hot: 0-1 range
}

df = pd.DataFrame(data)
print("Sample encoded data:")
print(df)
print(f"\nFeature ranges:")
for col in df.columns:
    print(f"{col}: {df[col].min():.1f} to {df[col].max():.1f}")

# Calculate distances between first two samples
sample1 = df.iloc[0].values
sample2 = df.iloc[1].values

print(f"\nDistance calculation between samples 1 and 2:")
print(f"Sample 1: {sample1}")
print(f"Sample 2: {sample2}")

# Without scaling
diff_unscaled = sample1 - sample2
distance_unscaled = np.sqrt(np.sum(diff_unscaled**2))
print(f"\nWithout scaling:")
print(f"Differences: {diff_unscaled}")
print(f"Euclidean distance: {distance_unscaled:.2f}")
print("Notice: Age and Weight differences dominate!")

# With scaling
from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
df_scaled = scaler.fit_transform(df)

sample1_scaled = df_scaled[0]
sample2_scaled = df_scaled[1]
diff_scaled = sample1_scaled - sample2_scaled
distance_scaled = np.sqrt(np.sum(diff_scaled**2))

print(f"\nWith StandardScaler:")
print(f"Scaled sample 1: {sample1_scaled}")
print(f"Scaled sample 2: {sample2_scaled}")
print(f"Differences: {diff_scaled}")
print(f"Euclidean distance: {distance_scaled:.2f}")
print("Notice: All features contribute equally!")

print("\n" + "=" * 60)
print("CONCLUSION: Scale ALL features for fair clustering!")
print("=" * 60)
