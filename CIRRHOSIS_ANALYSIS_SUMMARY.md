# Cirrhosis Survival Prediction - Machine Learning Analysis

## Project Overview

This project implements a comprehensive machine learning pipeline for predicting survival status in patients with liver cirrhosis. The analysis is based on a Mayo Clinic study on primary biliary cirrhosis (PBC) conducted from 1974 to 1984.

## Dataset Information

- **Source**: Mayo Clinic study on primary biliary cirrhosis (PBC)
- **Study Period**: 1974-1984
- **Sample Size**: 418 patients
- **Features**: 17 clinical features
- **Target Variable**: Survival status with 3 classes:
  - 0 = D (Death)
  - 1 = C (Censored)
  - 2 = CL (Censored due to liver transplantation)

## Implementation Files

### Main Analysis Notebook
- **File**: `Task_4C_MachineLearning.ipynb`
- **Description**: Complete implementation of the cirrhosis survival prediction pipeline

### Test Script
- **File**: `test_cirrhosis_analysis.py`
- **Description**: Standalone test script that validates the implementation with synthetic data

## Analysis Pipeline

### 1. Data Loading and Exploration
- **Function**: `load_data()`
- **Features**: 
  - Local file fallback logic
  - Remote URL download capability
  - Error handling and validation
- **Data Sources**:
  - Local file: `C:/datasets/cirrhosis.csv`
  - Remote URL: Kaggle cirrhosis dataset

### 2. Missing Values Analysis and Handling

#### Analysis Function: `analyze_missing_values()`
- Identifies missing values per feature
- Calculates missing value percentages
- Provides comprehensive missing data report

#### Imputation Function: `fill_missing_values()`
- **Strategy Selection**: `determine_imputation_strategy()`
  - **Categorical features**: Mode (most frequent value)
  - **Numerical features**: 
    - Mean (if |skewness| ≤ 1)
    - Median (if |skewness| > 1)
- **Rationale**: Skewness-based strategy ensures robust imputation for non-normal distributions

### 3. Train-Test Split (8:2 Ratio)

#### Function: `split_dataset()`
- **Split Ratio**: 80% training, 20% testing
- **Stratification**: Maintains class distribution across splits
- **Random State**: 42 (for reproducibility)
- **Validation**: Ensures consistent feature types across splits

### 4. Feature Type Analysis

#### Function: `analyze_feature_types()`
- **Categorical Features**: Object/category data types
- **Continuous Features**: Numerical data types
- **Consistency Check**: Validates feature types between train/test sets

**Expected Feature Types**:
- **Categorical**: Drug, Sex, Ascites, Hepatomegaly, Spiders, Edema
- **Continuous**: Age, Bilirubin, Cholesterol, Albumin, Copper, Alk_Phos, SGOT, Triglycerides, Platelets, Prothrombin, Stage

### 5. Categorical Feature Encoding

#### Function: `encode_categorical_features()`
- **Binary Features**: Label Encoding
  - Maps two categories to 0 and 1
  - Memory efficient for binary variables
- **Multi-class Features**: One-Hot Encoding
  - Creates dummy variables with drop_first=True
  - Handles unseen categories in test set
  - Maintains column consistency between train/test

### 6. Label Distribution Analysis

#### Function: `analyze_label_distribution()`
- **Distribution Metrics**:
  - Class counts and percentages
  - Imbalance ratio calculation
  - Balance status classification
- **Balance Categories**:
  - **Balanced**: Ratio ≤ 1.5
  - **Moderately Imbalanced**: 1.5 < Ratio ≤ 3.0
  - **Highly Imbalanced**: Ratio > 3.0
- **Visualization**: Bar charts showing train/test distributions

## Test Results (Synthetic Data)

### Dataset Characteristics
- **Shape**: 418 samples × 21 features
- **Missing Values**: 8-11% in 6 features (realistic simulation)
- **Feature Distribution**: 6 categorical, 12 continuous

### Processing Results
- **After Encoding**: 334 training samples × 19 features
- **Test Set**: 84 samples × 19 features
- **Label Distribution**:
  - Class 0 (Death): 25.7%
  - Class 1 (Censored): 56.0%
  - Class 2 (Liver transplant): 18.3%
- **Balance Status**: Highly Imbalanced (ratio: 3.07)

## Key Features of Implementation

### 1. Robust Data Loading
- Fallback mechanism from local to remote sources
- Comprehensive error handling
- Automatic shape and type reporting

### 2. Intelligent Missing Value Handling
- Skewness-based imputation strategy
- Separate handling for categorical vs numerical features
- Detailed imputation reporting

### 3. Proper Train-Test Splitting
- Stratified sampling maintains class balance
- Consistent feature type validation
- Reproducible random state

### 4. Smart Categorical Encoding
- Automatic detection of binary vs multi-class features
- Appropriate encoding method selection
- Test set compatibility ensured

### 5. Comprehensive Balance Analysis
- Multiple balance metrics
- Visual distribution analysis
- Actionable recommendations for imbalanced data

## Recommendations for Imbalanced Data

When the dataset is imbalanced (as expected with medical data):

1. **Sampling Techniques**:
   - SMOTE (Synthetic Minority Oversampling Technique)
   - Random undersampling of majority class
   - Combination of over/undersampling

2. **Algorithm Considerations**:
   - Use class weights in algorithms
   - Ensemble methods (Random Forest, XGBoost)
   - Cost-sensitive learning

3. **Evaluation Metrics**:
   - F1-score (especially macro/weighted)
   - AUC-ROC for multi-class
   - Precision-Recall curves
   - Confusion matrix analysis

4. **Cross-Validation**:
   - Stratified K-fold cross-validation
   - Ensure consistent class distribution across folds

## Usage Instructions

### Running the Main Notebook
1. Open `Task_4C_MachineLearning.ipynb` in Jupyter
2. Execute cells sequentially
3. Ensure dataset is available locally or internet connection for download

### Running the Test Script
```bash
python test_cirrhosis_analysis.py
```

### Expected Outputs
- Missing value analysis report
- Imputation strategy summary
- Feature type classification
- Encoding transformation details
- Label distribution visualization
- Balance analysis and recommendations

## File Structure
```
├── Task_4C_MachineLearning.ipynb          # Main analysis notebook
├── test_cirrhosis_analysis.py             # Test script with synthetic data
├── cirrhosis_label_distribution.png       # Generated visualization
└── CIRRHOSIS_ANALYSIS_SUMMARY.md         # This documentation
```

## Dependencies
- pandas
- numpy
- scikit-learn
- matplotlib
- seaborn
- requests (for data download)

This implementation provides a complete, production-ready pipeline for cirrhosis survival prediction analysis, with robust error handling, comprehensive documentation, and proper machine learning best practices.
