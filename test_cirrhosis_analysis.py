#!/usr/bin/env python3
"""
Test script for cirrhosis survival prediction analysis
"""

import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import LabelEncoder
import matplotlib.pyplot as plt
import seaborn as sns

# Create a sample cirrhosis dataset for testing
def create_sample_cirrhosis_data():
    """Create a sample dataset that mimics the cirrhosis dataset structure"""
    np.random.seed(42)
    n_samples = 418
    
    # Create sample data
    data = {
        'ID': range(1, n_samples + 1),
        'N_Days': np.random.randint(41, 4795, n_samples),
        'Status': np.random.choice(['D', 'C', 'CL'], n_samples, p=[0.3, 0.5, 0.2]),
        'Drug': np.random.choice(['D-penicillamine', 'Placebo'], n_samples),
        'Age': np.random.randint(26, 78, n_samples),
        'Sex': np.random.choice(['M', 'F'], n_samples, p=[0.1, 0.9]),
        'Ascites': np.random.choice(['Y', 'N'], n_samples, p=[0.2, 0.8]),
        'Hepatomegaly': np.random.choice(['Y', 'N'], n_samples, p=[0.6, 0.4]),
        'Spiders': np.random.choice(['Y', 'N'], n_samples, p=[0.3, 0.7]),
        'Edema': np.random.choice(['Y', 'N', 'S'], n_samples, p=[0.1, 0.8, 0.1]),
        'Bilirubin': np.random.lognormal(0.5, 1.2, n_samples),
        'Cholesterol': np.random.normal(365, 230, n_samples),
        'Albumin': np.random.normal(3.5, 0.4, n_samples),
        'Copper': np.random.lognormal(3.5, 1.0, n_samples),
        'Alk_Phos': np.random.lognormal(6.5, 0.8, n_samples),
        'SGOT': np.random.lognormal(4.5, 0.6, n_samples),
        'Tryglicerides': np.random.lognormal(4.8, 0.5, n_samples),
        'Platelets': np.random.normal(257, 98, n_samples),
        'Prothrombin': np.random.normal(10.7, 1.0, n_samples),
        'Stage': np.random.choice([1, 2, 3, 4], n_samples, p=[0.1, 0.3, 0.4, 0.2])
    }
    
    # Add some missing values to simulate real data
    df = pd.DataFrame(data)
    
    # Introduce missing values
    missing_cols = ['Cholesterol', 'Copper', 'Alk_Phos', 'SGOT', 'Tryglicerides', 'Platelets']
    for col in missing_cols:
        if col in df.columns:
            missing_mask = np.random.random(len(df)) < 0.1  # 10% missing
            df.loc[missing_mask, col] = np.nan
    
    # Convert Status to numeric (0=D, 1=C, 2=CL)
    status_mapping = {'D': 0, 'C': 1, 'CL': 2}
    df['Status_numeric'] = df['Status'].map(status_mapping)
    
    return df

def test_cirrhosis_analysis():
    """Test the cirrhosis analysis pipeline"""
    print("=" * 60)
    print("TESTING CIRRHOSIS SURVIVAL PREDICTION ANALYSIS")
    print("=" * 60)
    
    # Create sample data
    df = create_sample_cirrhosis_data()
    print(f"Created sample dataset with shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # 1. Missing values analysis
    print("\n1. MISSING VALUES ANALYSIS:")
    print("-" * 30)
    missing_counts = df.isnull().sum()
    for col, count in missing_counts.items():
        if count > 0:
            pct = (count / len(df)) * 100
            print(f"{col}: {count} missing ({pct:.1f}%)")
    
    # 2. Fill missing values (simple strategy for testing)
    print("\n2. FILLING MISSING VALUES:")
    print("-" * 30)
    df_filled = df.copy()
    for col in df.columns:
        if df[col].isnull().sum() > 0:
            if df[col].dtype in ['object', 'category']:
                mode_val = df[col].mode().iloc[0] if not df[col].mode().empty else 'Unknown'
                df_filled[col].fillna(mode_val, inplace=True)
                print(f"{col}: filled with mode '{mode_val}'")
            else:
                median_val = df[col].median()
                df_filled[col].fillna(median_val, inplace=True)
                print(f"{col}: filled with median {median_val:.2f}")
    
    # 3. Train-test split
    print("\n3. TRAIN-TEST SPLIT:")
    print("-" * 30)
    target_col = 'Status_numeric'
    X = df_filled.drop(columns=[target_col, 'Status', 'ID'])  # Remove ID and original Status
    y = df_filled[target_col]
    
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"Training set: {X_train.shape}")
    print(f"Test set: {X_test.shape}")
    
    # 4. Feature type analysis
    print("\n4. FEATURE TYPE ANALYSIS:")
    print("-" * 30)
    categorical_features = X_train.select_dtypes(include=['object', 'category']).columns.tolist()
    continuous_features = X_train.select_dtypes(include=[np.number]).columns.tolist()
    
    print(f"Categorical features ({len(categorical_features)}): {categorical_features}")
    print(f"Continuous features ({len(continuous_features)}): {continuous_features}")
    
    # 5. Encode categorical features
    print("\n5. CATEGORICAL ENCODING:")
    print("-" * 30)
    X_train_encoded = X_train.copy()
    X_test_encoded = X_test.copy()
    
    for feature in categorical_features:
        unique_count = X_train[feature].nunique()
        print(f"\n{feature} ({unique_count} unique values):")
        
        if unique_count == 2:
            # Binary - use Label Encoding
            le = LabelEncoder()
            X_train_encoded[feature] = le.fit_transform(X_train[feature])
            X_test_encoded[feature] = le.transform(X_test[feature])
            print(f"  Label Encoding: {dict(zip(le.classes_, le.transform(le.classes_)))}")
        else:
            # Multi-class - use One-Hot Encoding
            train_dummies = pd.get_dummies(X_train[feature], prefix=feature, drop_first=True)
            test_dummies = pd.get_dummies(X_test[feature], prefix=feature, drop_first=True)
            
            # Ensure test set has same columns as training set
            for col in train_dummies.columns:
                if col not in test_dummies.columns:
                    test_dummies[col] = 0
            test_dummies = test_dummies[train_dummies.columns]
            
            # Replace original column with dummy columns
            X_train_encoded = X_train_encoded.drop(columns=[feature])
            X_test_encoded = X_test_encoded.drop(columns=[feature])
            X_train_encoded = pd.concat([X_train_encoded, train_dummies], axis=1)
            X_test_encoded = pd.concat([X_test_encoded, test_dummies], axis=1)
            
            print(f"  One-Hot Encoding: {train_dummies.columns.tolist()}")
    
    print(f"\nAfter encoding:")
    print(f"Training set: {X_train_encoded.shape}")
    print(f"Test set: {X_test_encoded.shape}")
    
    # 6. Label distribution analysis
    print("\n6. LABEL DISTRIBUTION ANALYSIS:")
    print("-" * 30)
    
    train_counts = y_train.value_counts().sort_index()
    train_pct = y_train.value_counts(normalize=True).sort_index() * 100
    
    print("Training set distribution:")
    for label in train_counts.index:
        count = train_counts[label]
        pct = train_pct[label]
        status_name = {0: 'D (Death)', 1: 'C (Censored)', 2: 'CL (Liver transplant)'}[label]
        print(f"  Class {label} ({status_name}): {count} samples ({pct:.1f}%)")
    
    # Balance analysis
    max_count = train_counts.max()
    min_count = train_counts.min()
    imbalance_ratio = max_count / min_count
    
    print(f"\nImbalance ratio: {imbalance_ratio:.2f}")
    
    if imbalance_ratio <= 1.5:
        balance_status = "BALANCED"
    elif imbalance_ratio <= 3.0:
        balance_status = "MODERATELY IMBALANCED"
    else:
        balance_status = "HIGHLY IMBALANCED"
    
    print(f"Dataset balance: {balance_status}")
    
    # Create visualization
    plt.figure(figsize=(10, 4))
    
    plt.subplot(1, 2, 1)
    train_counts.plot(kind='bar', color='skyblue', alpha=0.7)
    plt.title('Training Set Label Distribution')
    plt.xlabel('Survival Status')
    plt.ylabel('Count')
    plt.xticks(rotation=0)
    
    # Add percentage labels
    for i, (label, count) in enumerate(train_counts.items()):
        pct = train_pct[label]
        plt.text(i, count + max_count*0.01, f'{pct:.1f}%', ha='center', va='bottom')
    
    plt.subplot(1, 2, 2)
    test_counts = y_test.value_counts().sort_index()
    test_pct = y_test.value_counts(normalize=True).sort_index() * 100
    
    test_counts.plot(kind='bar', color='lightcoral', alpha=0.7)
    plt.title('Test Set Label Distribution')
    plt.xlabel('Survival Status')
    plt.ylabel('Count')
    plt.xticks(rotation=0)
    
    # Add percentage labels
    test_max = test_counts.max()
    for i, (label, count) in enumerate(test_counts.items()):
        pct = test_pct[label]
        plt.text(i, count + test_max*0.01, f'{pct:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('cirrhosis_label_distribution.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    print("\n" + "=" * 60)
    print("TEST COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print(f"Final dataset shapes:")
    print(f"  X_train_encoded: {X_train_encoded.shape}")
    print(f"  X_test_encoded: {X_test_encoded.shape}")
    print(f"  y_train: {y_train.shape}")
    print(f"  y_test: {y_test.shape}")
    print(f"Dataset balance: {balance_status}")
    
    return X_train_encoded, X_test_encoded, y_train, y_test, balance_status

if __name__ == "__main__":
    test_cirrhosis_analysis()
