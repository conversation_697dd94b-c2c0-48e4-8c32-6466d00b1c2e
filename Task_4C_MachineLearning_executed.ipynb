{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cb67adb1", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:46.825142Z", "iopub.status.busy": "2025-07-16T23:07:46.824135Z", "iopub.status.idle": "2025-07-16T23:07:57.019714Z", "shell.execute_reply": "2025-07-16T23:07:57.019714Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Numpy Version 1.26.1\n", "Pandas Version 2.3.1\n", "Seaborn Version 0.13.2\n"]}], "source": ["# Import required libraries for data analysis and HTTP requests\n", "import pandas as pd      # Data manipulation and analysis\n", "import numpy as np       # Numerical operations\n", "import requests          # For downloading data from the web\n", "from io import StringIO  # For reading CSV data from string\n", "# Import additional libraries for preprocessing and visualization\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.preprocessing import MinMaxScaler, StandardScaler\n", "from sklearn.preprocessing import LabelEncoder, OneHotEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.cluster import KMeans\n", "from sklearn.metrics import silhouette_score\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set plotting style\n", "plt.style.use('default')\n", "sns.set_palette('husl')\n", "\n", "print('Numpy Version',np.__version__)\n", "print('Pandas Version',pd.__version__)\n", "print('Seaborn Version',sns.__version__)\n"]}, {"cell_type": "code", "execution_count": 2, "id": "83695dcc", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.022720Z", "iopub.status.busy": "2025-07-16T23:07:57.022720Z", "iopub.status.idle": "2025-07-16T23:07:57.028629Z", "shell.execute_reply": "2025-07-16T23:07:57.028629Z"}}, "outputs": [], "source": ["def load_data(local_file_path=None, url=None):\n", "    \"\"\"\n", "    Load dataset from local file or remote URL with fallback logic\n", "    \n", "    Parameters:\n", "    -----------\n", "    local_file_path : str, optional\n", "        Path to local CSV file. If provided, will try to load from this file first.\n", "    url : str, optional\n", "        URL to download data from if local file fails or is not provided.\n", "    \n", "    Returns:\n", "    --------\n", "    pandas.DataFrame or None\n", "        Returns the loaded DataFrame or None if both local and remote loading fail\n", "    \"\"\"\n", "    import os\n", "    \n", "    # Try loading from local file first if path is provided\n", "    if local_file_path:\n", "        print(f\"Attempting to load dataset from local file: {local_file_path}\")\n", "        try:\n", "            if not os.path.exists(local_file_path):\n", "                print(f\"Local file not found: {local_file_path}\")\n", "                if url:\n", "                    print(\"Falling back to downloading from URL...\")\n", "                else:\n", "                    return None\n", "            else:\n", "                df = pd.read_csv(local_file_path)\n", "                print(f\"Dataset loaded successfully from local file! Shape: {df.shape}\")\n", "                return df\n", "                \n", "        except Exception as e:\n", "            print(f\"Error reading local file: {e}\")\n", "            if url:\n", "                print(\"Falling back to downloading from URL...\")\n", "            else:\n", "                return None\n", "    \n", "    # Load from URL (either as primary method or fallback)\n", "    if url:\n", "        print(f\"Loading dataset from URL...\")\n", "        \n", "        try:\n", "            response = requests.get(url, timeout=30)\n", "            response.raise_for_status()\n", "            \n", "            # Read CSV data from the response\n", "            df = pd.read_csv(StringIO(response.text))\n", "            print(f\"Dataset loaded successfully from URL! Shape: {df.shape}\")\n", "            return df\n", "            \n", "        except Exception as e:\n", "            print(f\"Error loading from URL: {e}\")\n", "            return None\n", "    \n", "    print(\"No data source provided\")\n", "    return None"]}, {"cell_type": "markdown", "id": "d3cbe87e", "metadata": {}, "source": ["# Cirrhosis Survival Prediction - Machine Learning Task\n", "\n", "## Background\n", "Cirrhosis results from prolonged liver damage, leading to extensive scarring, often due to conditions like hepatitis or chronic alcohol consumption. The data provided is a subset sourced from a Mayo Clinic study on primary biliary cirrhosis (PBC) of the liver carried out from 1974 to 1984.\n", "\n", "This is a dataset to develop and validate machine learning algorithms for predicting the survival status of the collected patients. There are 418 patients in the data set, and each patient has 17 collected features. The aim of this task is to utilize 17 clinical features for predicting survival state of patients with liver cirrhosis.\n", "\n", "**Survival states include:**\n", "- 0 = D (death)\n", "- 1 = C (censored) \n", "- 2 = CL (censored due to liver transplantation)\n", "\n", "### 1. <PERSON>ad and explore the cirrhosis dataset"]}, {"cell_type": "code", "execution_count": 3, "id": "b8706a09", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.031636Z", "iopub.status.busy": "2025-07-16T23:07:57.031636Z", "iopub.status.idle": "2025-07-16T23:07:57.058545Z", "shell.execute_reply": "2025-07-16T23:07:57.058545Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempting to load dataset from local file: C:/datasets/cirrhosis.csv\n", "Dataset loaded successfully from local file! Shape: (418, 20)\n", "============================================================\n", "CIRRHOSIS DATASET INFO\n", "============================================================\n", "Dataset shape: (418, 20)\n", "Columns: ['ID', 'N_<PERSON>', 'Status', 'Drug', 'Age', 'Sex', 'Ascites', 'Hepatomegaly', 'Spider<PERSON>', 'Ed<PERSON>', 'B<PERSON><PERSON>bin', 'Cholesterol', 'Albumin', '<PERSON>', '<PERSON>k_Phos', 'SGOT', 'Tryglicerides', 'Platelets', 'Prothrombin', 'Stage']\n", "Data types:\n", "ID                 int64\n", "N_Days             int64\n", "Status            object\n", "Drug              object\n", "Age                int64\n", "Sex               object\n", "Ascites           object\n", "Hepatomegaly      object\n", "Spiders           object\n", "Edema             object\n", "Bilirubin        float64\n", "Cholesterol      float64\n", "Albumin          float64\n", "Copper           float64\n", "Alk_Phos         float64\n", "SGOT             float64\n", "Tryglicerides    float64\n", "Platelets        float64\n", "Prothrombin      float64\n", "Stage            float64\n", "dtype: object\n", "The number of rows (observations) is 418 \n", "The number of columns (variables) is 20\n", "============================================================\n", "Overall missing values: 12.36%\n"]}], "source": ["# Load cirrhosis dataset\n", "cirrhosis_url = \"https://raw.githubusercontent.com/plotly/datasets/master/liver_cirrhosis.csv\"\n", "local_data_path = \"C:/datasets/cirrhosis.csv\"\n", "\n", "df = load_data(local_file_path=local_data_path, url=cirrhosis_url)\n", "\n", "if df is None:\n", "    print('Failed to load dataset. Exiting...')\n", "else:\n", "    print(\"=\" * 60)\n", "    print(\"CIRRHOSIS DATASET INFO\")\n", "    print(\"=\" * 60)\n", "    print(f'Dataset shape: {df.shape}')\n", "    print(f'Columns: {list(df.columns)}')\n", "    print(f'Data types:\\n{df.dtypes}')\n", "    print('The number of rows (observations) is',df.shape[0],'\\n''The number of columns (variables) is',df.shape[1])\n", "    print(\"=\" * 60)\n", "    \n", "    # Calculate overall percentage of missing values\n", "    overall_missing_pct = (df.isnull().sum().sum() / df.size) * 100\n", "    print(f'Overall missing values: {overall_missing_pct:.2f}%')"]}, {"cell_type": "code", "execution_count": 4, "id": "a4016d1b", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.061550Z", "iopub.status.busy": "2025-07-16T23:07:57.060549Z", "iopub.status.idle": "2025-07-16T23:07:57.133405Z", "shell.execute_reply": "2025-07-16T23:07:57.133405Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset Info:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 418 entries, 0 to 417\n", "Data columns (total 20 columns):\n", " #   Column         Non-Null Count  Dtype  \n", "---  ------         --------------  -----  \n", " 0   ID             418 non-null    int64  \n", " 1   N_Days         418 non-null    int64  \n", " 2   Status         418 non-null    object \n", " 3   Drug           312 non-null    object \n", " 4   Age            418 non-null    int64  \n", " 5   Sex            418 non-null    object \n", " 6   Ascites        312 non-null    object \n", " 7   Hepatomegaly   312 non-null    object \n", " 8   Spiders        312 non-null    object \n", " 9   Edema          418 non-null    object \n", " 10  Bilirubin      418 non-null    float64\n", " 11  Cholesterol    284 non-null    float64\n", " 12  Albumin        418 non-null    float64\n", " 13  Copper         310 non-null    float64\n", " 14  Alk_Phos       312 non-null    float64\n", " 15  SGOT           312 non-null    float64\n", " 16  Tryglicerides  282 non-null    float64\n", " 17  Platelets      407 non-null    float64\n", " 18  Prothrombin    416 non-null    float64\n", " 19  Stage          412 non-null    float64\n", "dtypes: float64(10), int64(3), object(7)\n", "memory usage: 65.4+ KB\n", "\n", "First few rows:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>ID</th>\n", "      <th>N_Days</th>\n", "      <th>Status</th>\n", "      <th>Drug</th>\n", "      <th>Age</th>\n", "      <th>Sex</th>\n", "      <th>Ascites</th>\n", "      <th><PERSON><PERSON><PERSON>eg<PERSON></th>\n", "      <th>Spiders</th>\n", "      <th><PERSON><PERSON></th>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <th>Cholesterol</th>\n", "      <th>Albumin</th>\n", "      <th>Copper</th>\n", "      <th>Alk_Phos</th>\n", "      <th>SGOT</th>\n", "      <th>Tryglicerides</th>\n", "      <th>Platelets</th>\n", "      <th>Prothrombin</th>\n", "      <th>Stage</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>400</td>\n", "      <td>D</td>\n", "      <td>D-penicillamine</td>\n", "      <td>21464</td>\n", "      <td>F</td>\n", "      <td>Y</td>\n", "      <td>Y</td>\n", "      <td>Y</td>\n", "      <td>Y</td>\n", "      <td>14.5</td>\n", "      <td>261.0</td>\n", "      <td>2.60</td>\n", "      <td>156.0</td>\n", "      <td>1718.0</td>\n", "      <td>137.95</td>\n", "      <td>172.0</td>\n", "      <td>190.0</td>\n", "      <td>12.2</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>4500</td>\n", "      <td>C</td>\n", "      <td>D-penicillamine</td>\n", "      <td>20617</td>\n", "      <td>F</td>\n", "      <td>N</td>\n", "      <td>Y</td>\n", "      <td>Y</td>\n", "      <td>N</td>\n", "      <td>1.1</td>\n", "      <td>302.0</td>\n", "      <td>4.14</td>\n", "      <td>54.0</td>\n", "      <td>7394.8</td>\n", "      <td>113.52</td>\n", "      <td>88.0</td>\n", "      <td>221.0</td>\n", "      <td>10.6</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1012</td>\n", "      <td>D</td>\n", "      <td>D-penicillamine</td>\n", "      <td>25594</td>\n", "      <td>M</td>\n", "      <td>N</td>\n", "      <td>N</td>\n", "      <td>N</td>\n", "      <td>S</td>\n", "      <td>1.4</td>\n", "      <td>176.0</td>\n", "      <td>3.48</td>\n", "      <td>210.0</td>\n", "      <td>516.0</td>\n", "      <td>96.10</td>\n", "      <td>55.0</td>\n", "      <td>151.0</td>\n", "      <td>12.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1925</td>\n", "      <td>D</td>\n", "      <td>D-penicillamine</td>\n", "      <td>19994</td>\n", "      <td>F</td>\n", "      <td>N</td>\n", "      <td>Y</td>\n", "      <td>Y</td>\n", "      <td>S</td>\n", "      <td>1.8</td>\n", "      <td>244.0</td>\n", "      <td>2.54</td>\n", "      <td>64.0</td>\n", "      <td>6121.8</td>\n", "      <td>60.63</td>\n", "      <td>92.0</td>\n", "      <td>183.0</td>\n", "      <td>10.3</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>1504</td>\n", "      <td>CL</td>\n", "      <td>Placebo</td>\n", "      <td>13918</td>\n", "      <td>F</td>\n", "      <td>N</td>\n", "      <td>Y</td>\n", "      <td>Y</td>\n", "      <td>N</td>\n", "      <td>3.4</td>\n", "      <td>279.0</td>\n", "      <td>3.53</td>\n", "      <td>143.0</td>\n", "      <td>671.0</td>\n", "      <td>113.15</td>\n", "      <td>72.0</td>\n", "      <td>136.0</td>\n", "      <td>10.9</td>\n", "      <td>3.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   ID  N_Days Status             Drug    Age Sex Ascites Hepatomegaly Spiders  \\\n", "0   1     400      D  D-penicillamine  21464   F       Y            Y       Y   \n", "1   2    4500      C  D-penicillamine  20617   F       N            Y       Y   \n", "2   3    1012      D  D-penicillamine  25594   M       N            N       N   \n", "3   4    1925      D  D-penicillamine  19994   F       N            Y       Y   \n", "4   5    1504     CL          Placebo  13918   F       N            Y       Y   \n", "\n", "  Edema  Bilirubin  Cholesterol  Albumin  Copper  Alk_Phos    SGOT  \\\n", "0     Y       14.5        261.0     2.60   156.0    1718.0  137.95   \n", "1     N        1.1        302.0     4.14    54.0    7394.8  113.52   \n", "2     S        1.4        176.0     3.48   210.0     516.0   96.10   \n", "3     S        1.8        244.0     2.54    64.0    6121.8   60.63   \n", "4     N        3.4        279.0     3.53   143.0     671.0  113.15   \n", "\n", "   Tryglicerides  Platelets  Prothrombin  Stage  \n", "0          172.0      190.0         12.2    4.0  \n", "1           88.0      221.0         10.6    3.0  \n", "2           55.0      151.0         12.0    4.0  \n", "3           92.0      183.0         10.3    4.0  \n", "4           72.0      136.0         10.9    3.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dataset description:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>unique</th>\n", "      <th>top</th>\n", "      <th>freq</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>ID</th>\n", "      <td>418.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>209.5</td>\n", "      <td>120.810458</td>\n", "      <td>1.0</td>\n", "      <td>105.25</td>\n", "      <td>209.5</td>\n", "      <td>313.75</td>\n", "      <td>418.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>N_Days</th>\n", "      <td>418.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1917.782297</td>\n", "      <td>1104.672992</td>\n", "      <td>41.0</td>\n", "      <td>1092.75</td>\n", "      <td>1730.0</td>\n", "      <td>2613.5</td>\n", "      <td>4795.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Status</th>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>C</td>\n", "      <td>232</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Drug</th>\n", "      <td>312</td>\n", "      <td>2</td>\n", "      <td>D-penicillamine</td>\n", "      <td>158</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Age</th>\n", "      <td>418.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>18533.351675</td>\n", "      <td>3815.845055</td>\n", "      <td>9598.0</td>\n", "      <td>15644.5</td>\n", "      <td>18628.0</td>\n", "      <td>21272.5</td>\n", "      <td>28650.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sex</th>\n", "      <td>418</td>\n", "      <td>2</td>\n", "      <td>F</td>\n", "      <td>374</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ascites</th>\n", "      <td>312</td>\n", "      <td>2</td>\n", "      <td>N</td>\n", "      <td>288</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON>eg<PERSON></th>\n", "      <td>312</td>\n", "      <td>2</td>\n", "      <td>Y</td>\n", "      <td>160</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spiders</th>\n", "      <td>312</td>\n", "      <td>2</td>\n", "      <td>N</td>\n", "      <td>222</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON></th>\n", "      <td>418</td>\n", "      <td>3</td>\n", "      <td>N</td>\n", "      <td>354</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON><PERSON></th>\n", "      <td>418.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.220813</td>\n", "      <td>4.407506</td>\n", "      <td>0.3</td>\n", "      <td>0.8</td>\n", "      <td>1.4</td>\n", "      <td>3.4</td>\n", "      <td>28.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cholesterol</th>\n", "      <td>284.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>369.510563</td>\n", "      <td>231.944545</td>\n", "      <td>120.0</td>\n", "      <td>249.5</td>\n", "      <td>309.5</td>\n", "      <td>400.0</td>\n", "      <td>1775.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Albumin</th>\n", "      <td>418.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.49744</td>\n", "      <td>0.424972</td>\n", "      <td>1.96</td>\n", "      <td>3.2425</td>\n", "      <td>3.53</td>\n", "      <td>3.77</td>\n", "      <td>4.64</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Copper</th>\n", "      <td>310.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>97.648387</td>\n", "      <td>85.61392</td>\n", "      <td>4.0</td>\n", "      <td>41.25</td>\n", "      <td>73.0</td>\n", "      <td>123.0</td>\n", "      <td>588.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Alk_Phos</th>\n", "      <td>312.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1982.655769</td>\n", "      <td>2140.388824</td>\n", "      <td>289.0</td>\n", "      <td>871.5</td>\n", "      <td>1259.0</td>\n", "      <td>1980.0</td>\n", "      <td>13862.4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SGOT</th>\n", "      <td>312.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>122.556346</td>\n", "      <td>56.699525</td>\n", "      <td>26.35</td>\n", "      <td>80.6</td>\n", "      <td>114.7</td>\n", "      <td>151.9</td>\n", "      <td>457.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Tryglicerides</th>\n", "      <td>282.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>124.702128</td>\n", "      <td>65.148639</td>\n", "      <td>33.0</td>\n", "      <td>84.25</td>\n", "      <td>108.0</td>\n", "      <td>151.0</td>\n", "      <td>598.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Platelets</th>\n", "      <td>407.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>257.02457</td>\n", "      <td>98.325585</td>\n", "      <td>62.0</td>\n", "      <td>188.5</td>\n", "      <td>251.0</td>\n", "      <td>318.0</td>\n", "      <td>721.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Prothrombin</th>\n", "      <td>416.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>10.731731</td>\n", "      <td>1.022</td>\n", "      <td>9.0</td>\n", "      <td>10.0</td>\n", "      <td>10.6</td>\n", "      <td>11.1</td>\n", "      <td>18.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Stage</th>\n", "      <td>412.0</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.024272</td>\n", "      <td>0.882042</td>\n", "      <td>1.0</td>\n", "      <td>2.0</td>\n", "      <td>3.0</td>\n", "      <td>4.0</td>\n", "      <td>4.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               count unique              top freq          mean          std  \\\n", "ID             418.0    NaN              NaN  NaN         209.5   120.810458   \n", "N_Days         418.0    NaN              NaN  NaN   1917.782297  1104.672992   \n", "Status           418      3                C  232           NaN          NaN   \n", "Drug             312      2  D-penicillamine  158           NaN          NaN   \n", "Age            418.0    NaN              NaN  NaN  18533.351675  3815.845055   \n", "Sex              418      2                F  374           NaN          NaN   \n", "Ascites          312      2                N  288           NaN          NaN   \n", "Hepatomegaly     312      2                Y  160           NaN          NaN   \n", "Spiders          312      2                N  222           NaN          NaN   \n", "Edema            418      3                N  354           NaN          NaN   \n", "Bilirubin      418.0    NaN              NaN  NaN      3.220813     4.407506   \n", "Cholesterol    284.0    NaN              NaN  NaN    369.510563   231.944545   \n", "Albumin        418.0    NaN              NaN  NaN       3.49744     0.424972   \n", "Copper         310.0    NaN              NaN  NaN     97.648387     85.61392   \n", "Alk_Phos       312.0    NaN              NaN  NaN   1982.655769  2140.388824   \n", "SGOT           312.0    NaN              NaN  NaN    122.556346    56.699525   \n", "Tryglicerides  282.0    NaN              NaN  NaN    124.702128    65.148639   \n", "Platelets      407.0    NaN              NaN  NaN     257.02457    98.325585   \n", "Prothrombin    416.0    NaN              NaN  NaN     10.731731        1.022   \n", "Stage          412.0    NaN              NaN  NaN      3.024272     0.882042   \n", "\n", "                  min      25%      50%      75%      max  \n", "ID                1.0   105.25    209.5   313.75    418.0  \n", "N_Days           41.0  1092.75   1730.0   2613.5   4795.0  \n", "Status            NaN      NaN      NaN      NaN      NaN  \n", "Drug              NaN      NaN      NaN      NaN      NaN  \n", "Age            9598.0  15644.5  18628.0  21272.5  28650.0  \n", "Sex               NaN      NaN      NaN      NaN      NaN  \n", "Ascites           NaN      NaN      NaN      NaN      NaN  \n", "Hepatomegaly      NaN      NaN      NaN      NaN      NaN  \n", "Spiders           NaN      NaN      NaN      NaN      NaN  \n", "Edema             NaN      NaN      NaN      NaN      NaN  \n", "Bilirubin         0.3      0.8      1.4      3.4     28.0  \n", "Cholesterol     120.0    249.5    309.5    400.0   1775.0  \n", "Albumin          1.96   3.2425     3.53     3.77     4.64  \n", "Copper            4.0    41.25     73.0    123.0    588.0  \n", "Alk_Phos        289.0    871.5   1259.0   1980.0  13862.4  \n", "SGOT            26.35     80.6    114.7    151.9   457.25  \n", "Tryglicerides    33.0    84.25    108.0    151.0    598.0  \n", "Platelets        62.0    188.5    251.0    318.0    721.0  \n", "Prothrombin       9.0     10.0     10.6     11.1     18.0  \n", "Stage             1.0      2.0      3.0      4.0      4.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Display basic information about the dataset\n", "if df is not None:\n", "    print(\"Dataset Info:\")\n", "    df.info()\n", "    print(\"\\nFirst few rows:\")\n", "    display(df.head())\n", "    print(\"\\nDataset description:\")\n", "    display(df.describe(include='all').T)"]}, {"cell_type": "code", "execution_count": 5, "id": "df7b455e", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.135442Z", "iopub.status.busy": "2025-07-16T23:07:57.135442Z", "iopub.status.idle": "2025-07-16T23:07:57.140281Z", "shell.execute_reply": "2025-07-16T23:07:57.140281Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The number of categorical columns is 7\n", "The categorical columns are ['Status', 'Drug', 'Sex', 'Ascites', 'Hepatomegaly', 'Spider<PERSON>', 'Edema']\n", "The number of numerical columns is 13\n", "The numerical columns are ['ID', 'N_Days', 'Age', 'Bilirubin', 'Cholesterol', 'Albumin', 'Copper', 'Alk_Phos', 'SGOT', 'Tryglicerides', 'Platelets', 'Prothrombin', 'Stage']\n"]}], "source": ["# Identify feature types\n", "if df is not None:\n", "    print('The number of categorical columns is',df.select_dtypes(include='object').shape[1])\n", "    print('The categorical columns are',df.select_dtypes(include='object').columns.tolist())\n", "    \n", "    print('The number of numerical columns is',df.select_dtypes(include='number').shape[1])\n", "    print('The numerical columns are',df.select_dtypes(include='number').columns.tolist())"]}, {"cell_type": "markdown", "id": "c09e4d59", "metadata": {}, "source": ["### 1a. Missing Values Analysis and Handling"]}, {"cell_type": "code", "execution_count": 6, "id": "093520b9", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.143286Z", "iopub.status.busy": "2025-07-16T23:07:57.142317Z", "iopub.status.idle": "2025-07-16T23:07:57.148268Z", "shell.execute_reply": "2025-07-16T23:07:57.148268Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "MISSING VALUES ANALYSIS\n", "============================================================\n", "Total rows in dataset: 418\n", "Feature-wise missing values:\n", "----------------------------------------\n", "ID: 0 missing entries (0.00%)\n", "N_Days: 0 missing entries (0.00%)\n", "Status: 0 missing entries (0.00%)\n", "Drug: 106 missing entries (25.36%)\n", "Age: 0 missing entries (0.00%)\n", "Sex: 0 missing entries (0.00%)\n", "Ascites: 106 missing entries (25.36%)\n", "Hepatomegaly: 106 missing entries (25.36%)\n", "Spiders: 106 missing entries (25.36%)\n", "Edema: 0 missing entries (0.00%)\n", "Bilirubin: 0 missing entries (0.00%)\n", "Cholesterol: 134 missing entries (32.06%)\n", "Albumin: 0 missing entries (0.00%)\n", "Copper: 108 missing entries (25.84%)\n", "Alk_Phos: 106 missing entries (25.36%)\n", "SGOT: 106 missing entries (25.36%)\n", "Tryglicerides: 136 missing entries (32.54%)\n", "Platelets: 11 missing entries (2.63%)\n", "Prothrombin: 2 missing entries (0.48%)\n", "Stage: 6 missing entries (1.44%)\n"]}], "source": ["def analyze_missing_values(df):\n", "    \"\"\"\n", "    Analyze and print missing values for each feature\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"MISSING VALUES ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Count missing values for each column\n", "    missing_counts = df.isnull().sum()\n", "    total_rows = len(df)\n", "    print(f'Total rows in dataset: {total_rows}')\n", "    print(\"Feature-wise missing values:\")\n", "    print('-' * 40)\n", "    \n", "    # Print missing value count and percentage for each feature\n", "    for feature, missing_count in missing_counts.items():\n", "        missing_percentage = (missing_count / total_rows) * 100\n", "        print(f'{feature}: {missing_count} missing entries ({missing_percentage:.2f}%)')\n", "    \n", "    return missing_counts\n", "\n", "if df is not None:\n", "    missing_counts_df = analyze_missing_values(df)"]}, {"cell_type": "code", "execution_count": 7, "id": "17dc54bf", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.150309Z", "iopub.status.busy": "2025-07-16T23:07:57.150309Z", "iopub.status.idle": "2025-07-16T23:07:57.153946Z", "shell.execute_reply": "2025-07-16T23:07:57.153946Z"}}, "outputs": [], "source": ["def determine_imputation_strategy(df, feature):\n", "    \"\"\"\n", "    Determine whether to use mean or median for imputation based on data distribution\n", "    \"\"\"\n", "    # Use mode for categorical features\n", "    if df[feature].dtype in ['object', 'category']:\n", "        return 'mode'\n", "    # For numerical features, check skewness\n", "    skewness = df[feature].skew()\n", "    # Use median if highly skewed, mean if approximately normal\n", "    if abs(skewness) > 1:\n", "        return 'median'\n", "    else:\n", "        return 'mean'"]}, {"cell_type": "code", "execution_count": 8, "id": "46be4cdc", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.156953Z", "iopub.status.busy": "2025-07-16T23:07:57.156953Z", "iopub.status.idle": "2025-07-16T23:07:57.164220Z", "shell.execute_reply": "2025-07-16T23:07:57.164220Z"}}, "outputs": [], "source": ["def fill_missing_values(df):\n", "    \"\"\"\n", "    Fill missing values using appropriate imputation strategy for each feature\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"MISSING VALUES IMPUTATION\")\n", "    print(\"=\" * 60)\n", "    \n", "    df_filled = df.copy()\n", "    imputation_summary = {}\n", "    \n", "    # Find features with missing values\n", "    missing_counts = df.isnull().sum()\n", "    features_with_missing = missing_counts[missing_counts > 0].index\n", "    \n", "    if len(features_with_missing) == 0:\n", "        print(\"No missing values found in the dataset!\")\n", "        return df_filled, imputation_summary\n", "    \n", "    # Impute missing values for each feature\n", "    for feature in features_with_missing:\n", "        print(f'Processing feature: {feature}')\n", "        \n", "        if df[feature].dtype in ['object', 'category']:\n", "            # Use mode for categorical features\n", "            mode_value = df[feature].mode().iloc[0] if not df[feature].mode().empty else 'Unknown'\n", "            df_filled[feature].fillna(mode_value, inplace=True)\n", "            imputation_summary[feature] = {\n", "                'strategy': 'mode',\n", "                'replacement_value': mode_value,\n", "                'reason': 'Categorical feature - using most frequent value'\n", "            }\n", "            print(f'  Strategy: Mode')\n", "            print(f'  Replacement value: {mode_value}')\n", "        else:\n", "            # Use mean or median for numerical features based on skewness\n", "            strategy = determine_imputation_strategy(df, feature)\n", "            if strategy == 'mean':\n", "                replacement_value = df[feature].mean()\n", "                reason = f'Numerical feature with low skewness ({df[feature].skew():.3f}) - using mean'\n", "            else:\n", "                replacement_value = df[feature].median()\n", "                reason = f'Numerical feature with high skewness ({df[feature].skew():.3f}) - using median'\n", "            \n", "            df_filled[feature].fillna(replacement_value, inplace=True)\n", "            imputation_summary[feature] = {\n", "                'strategy': strategy,\n", "                'replacement_value': replacement_value,\n", "                'reason': reason\n", "            }\n", "            print(f'  Strategy: {strategy.capitalize()}')\n", "            print(f'  Replacement value: {replacement_value:.6f}')\n", "        print()\n", "    \n", "    return df_filled, imputation_summary"]}, {"cell_type": "code", "execution_count": 9, "id": "097a2366", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.167259Z", "iopub.status.busy": "2025-07-16T23:07:57.166226Z", "iopub.status.idle": "2025-07-16T23:07:57.185871Z", "shell.execute_reply": "2025-07-16T23:07:57.185871Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "MISSING VALUES IMPUTATION\n", "============================================================\n", "Processing feature: Drug\n", "  Strategy: Mode\n", "  Replacement value: D-penicillamine\n", "\n", "Processing feature: Ascites\n", "  Strategy: Mode\n", "  Replacement value: N\n", "\n", "Processing feature: Hepatomegaly\n", "  Strategy: Mode\n", "  Replacement value: Y\n", "\n", "Processing feature: Spiders\n", "  Strategy: Mode\n", "  Replacement value: N\n", "\n", "Processing feature: Cholesterol\n", "  Strategy: Median\n", "  Replacement value: 309.500000\n", "\n", "Processing feature: Copper\n", "  Strategy: Median\n", "  Replacement value: 73.000000\n", "\n", "Processing feature: Alk_Phos\n", "  Strategy: Median\n", "  Replacement value: 1259.000000\n", "\n", "Processing feature: SGOT\n", "  Strategy: Median\n", "  Replacement value: 114.700000\n", "\n", "Processing feature: Tryglicerides\n", "  Strategy: Median\n", "  Replacement value: 108.000000\n", "\n", "Processing feature: Platelets\n", "  Strategy: Mean\n", "  Replacement value: 257.024570\n", "\n", "Processing feature: Prothrombin\n", "  Strategy: Median\n", "  Replacement value: 10.600000\n", "\n", "Processing feature: Stage\n", "  Strategy: Mean\n", "  Replacement value: 3.024272\n", "\n", "\n", "Imputation Summary:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>strategy</th>\n", "      <th>replacement_value</th>\n", "      <th>reason</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Drug</th>\n", "      <td>mode</td>\n", "      <td>D-penicillamine</td>\n", "      <td>Categorical feature - using most frequent value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ascites</th>\n", "      <td>mode</td>\n", "      <td>N</td>\n", "      <td>Categorical feature - using most frequent value</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON>eg<PERSON></th>\n", "      <td>mode</td>\n", "      <td>Y</td>\n", "      <td>Categorical feature - using most frequent value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Spiders</th>\n", "      <td>mode</td>\n", "      <td>N</td>\n", "      <td>Categorical feature - using most frequent value</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Cholesterol</th>\n", "      <td>median</td>\n", "      <td>309.5</td>\n", "      <td>Numerical feature with high skewness (3.409) -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Copper</th>\n", "      <td>median</td>\n", "      <td>73.0</td>\n", "      <td>Numerical feature with high skewness (2.304) -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Alk_Phos</th>\n", "      <td>median</td>\n", "      <td>1259.0</td>\n", "      <td>Numerical feature with high skewness (2.993) -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SGOT</th>\n", "      <td>median</td>\n", "      <td>114.7</td>\n", "      <td>Numerical feature with high skewness (1.449) -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Tryglicerides</th>\n", "      <td>median</td>\n", "      <td>108.0</td>\n", "      <td>Numerical feature with high skewness (2.524) -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Platelets</th>\n", "      <td>mean</td>\n", "      <td>257.02457</td>\n", "      <td>Numerical feature with low skewness (0.627) - ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Prothrombin</th>\n", "      <td>median</td>\n", "      <td>10.6</td>\n", "      <td>Numerical feature with high skewness (2.223) -...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Stage</th>\n", "      <td>mean</td>\n", "      <td>3.024272</td>\n", "      <td>Numerical feature with low skewness (-0.496) -...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              strategy replacement_value  \\\n", "Drug              mode   D-penicillamine   \n", "Ascites           mode                 N   \n", "Hepatomegaly      mode                 Y   \n", "Spiders           mode                 N   \n", "Cholesterol     median             309.5   \n", "Copper          median              73.0   \n", "Alk_Phos        median            1259.0   \n", "SGOT            median             114.7   \n", "Tryglicerides   median             108.0   \n", "Platelets         mean         257.02457   \n", "Prothrombin     median              10.6   \n", "Stage             mean          3.024272   \n", "\n", "                                                          reason  \n", "Drug             Categorical feature - using most frequent value  \n", "Ascites          Categorical feature - using most frequent value  \n", "Hepatomegaly     Categorical feature - using most frequent value  \n", "Spiders          Categorical feature - using most frequent value  \n", "Cholesterol    Numerical feature with high skewness (3.409) -...  \n", "Copper         Numerical feature with high skewness (2.304) -...  \n", "Alk_Phos       Numerical feature with high skewness (2.993) -...  \n", "SGOT           Numerical feature with high skewness (1.449) -...  \n", "Tryglicerides  Numerical feature with high skewness (2.524) -...  \n", "Platelets      Numerical feature with low skewness (0.627) - ...  \n", "Prothrombin    Numerical feature with high skewness (2.223) -...  \n", "Stage          Numerical feature with low skewness (-0.496) -...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "============================================================\n", "VERIFICATION FOR MISSING VALUES\n", "============================================================\n", "Total missing values after imputation: 0\n", "All missing values have been successfully filled!\n"]}], "source": ["# Apply missing value imputation\n", "if df is not None:\n", "    df_filled, imputation_summary = fill_missing_values(df)\n", "    \n", "    # Display imputation summary\n", "    if imputation_summary:\n", "        imputation_summary_df = pd.DataFrame(imputation_summary).T\n", "        print(\"\\nImputation Summary:\")\n", "        display(imputation_summary_df)\n", "    \n", "    # Verify no missing values remain\n", "    print('\\n' + '=' * 60)\n", "    print('VERIFICATION FOR MISSING VALUES')\n", "    print('=' * 60)\n", "    remaining_missing = df_filled.isnull().sum().sum()\n", "    print(f'Total missing values after imputation: {remaining_missing}')\n", "    if remaining_missing == 0:\n", "        print('All missing values have been successfully filled!')\n", "    else:\n", "        print('Warning: Some missing values still remain!')"]}, {"cell_type": "markdown", "id": "fba63d3f", "metadata": {}, "source": ["### 1b. Split the dataset into training and test sets (8:2 ratio)"]}, {"cell_type": "code", "execution_count": 10, "id": "fd0751ed", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.188877Z", "iopub.status.busy": "2025-07-16T23:07:57.188877Z", "iopub.status.idle": "2025-07-16T23:07:57.193447Z", "shell.execute_reply": "2025-07-16T23:07:57.193447Z"}}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "\n", "def split_dataset(df, target_column, test_size=0.2, random_state=42):\n", "    \"\"\"\n", "    Split dataset into training and test sets\n", "    \n", "    Parameters:\n", "    -----------\n", "    df : pandas.DataFrame\n", "        Input dataframe\n", "    target_column : str\n", "        Name of the target column\n", "    test_size : float\n", "        Proportion of dataset to include in test split\n", "    random_state : int\n", "        Random state for reproducibility\n", "    \n", "    Returns:\n", "    --------\n", "    X_train, X_test, y_train, y_test\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"DATASET SPLITTING\")\n", "    print(\"=\" * 60)\n", "    \n", "    if target_column not in df.columns:\n", "        print(f\"Target column '{target_column}' not found in dataset.\")\n", "        print(f\"Available columns: {list(df.columns)}\")\n", "        return None, None, None, None\n", "    \n", "    # Separate features and target\n", "    X = df.drop(columns=[target_column])\n", "    y = df[target_column]\n", "    \n", "    # Split the data\n", "    X_train, X_test, y_train, y_test = train_test_split(\n", "        X, y, test_size=test_size, random_state=random_state, stratify=y\n", "    )\n", "    \n", "    print(f\"Original dataset shape: {df.shape}\")\n", "    print(f\"Training set shape: {X_train.shape}\")\n", "    print(f\"Test set shape: {X_test.shape}\")\n", "    print(f\"Training set ratio: {len(X_train)/len(df)*100:.1f}%\")\n", "    print(f\"Test set ratio: {len(X_test)/len(df)*100:.1f}%\")\n", "    \n", "    return X_train, X_test, y_train, y_test"]}, {"cell_type": "code", "execution_count": 11, "id": "8a713fcc", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.196482Z", "iopub.status.busy": "2025-07-16T23:07:57.196482Z", "iopub.status.idle": "2025-07-16T23:07:57.206746Z", "shell.execute_reply": "2025-07-16T23:07:57.206746Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Using 'Status' as target column\n", "============================================================\n", "DATASET SPLITTING\n", "============================================================\n", "Original dataset shape: (418, 20)\n", "Training set shape: (334, 19)\n", "Test set shape: (84, 19)\n", "Training set ratio: 79.9%\n", "Test set ratio: 20.1%\n"]}], "source": ["# Split the dataset\n", "if df_filled is not None:\n", "    # Identify the target column (survival status)\n", "    # Common names for survival status in cirrhosis datasets\n", "    possible_target_names = ['Status', 'status', 'Survival', 'survival', 'Stage', 'stage']\n", "    target_column = None\n", "    \n", "    for col in possible_target_names:\n", "        if col in df_filled.columns:\n", "            target_column = col\n", "            break\n", "    \n", "    if target_column is None:\n", "        print(\"Target column not found. Available columns:\")\n", "        print(list(df_filled.columns))\n", "        print(\"Please specify the correct target column name.\")\n", "    else:\n", "        print(f\"Using '{target_column}' as target column\")\n", "        X_train, X_test, y_train, y_test = split_dataset(df_filled, target_column, test_size=0.2, random_state=42)"]}, {"cell_type": "markdown", "id": "a51ba212", "metadata": {}, "source": ["### 1c. Feature Type Analysis (Training and Test Data)"]}, {"cell_type": "code", "execution_count": 12, "id": "70592914", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.208775Z", "iopub.status.busy": "2025-07-16T23:07:57.208775Z", "iopub.status.idle": "2025-07-16T23:07:57.216228Z", "shell.execute_reply": "2025-07-16T23:07:57.216228Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "FEATURE TYPE ANALYSIS\n", "============================================================\n", "TRAINING SET:\n", "------------------------------\n", "Categorical features (6): ['Drug', 'Sex', 'Ascites', 'Hepatomegaly', 'Spiders', 'Edema']\n", "Continuous features (13): ['ID', 'N_Days', 'Age', 'Bilirubin', 'Cholesterol', 'Albumin', '<PERSON>', '<PERSON>k_Phos', 'SGOT', 'Tryglicerides', 'Platelets', 'Prothrombin', 'Stage']\n", "\n", "TEST SET:\n", "------------------------------\n", "Categorical features (6): ['Drug', 'Sex', 'Ascites', 'Hepatomegaly', 'Spiders', 'Edema']\n", "Continuous features (13): ['ID', 'N_Days', 'Age', 'Bilirubin', 'Cholesterol', 'Albumin', '<PERSON>', '<PERSON>k_Phos', 'SGOT', 'Tryglicerides', 'Platelets', 'Prothrombin', 'Stage']\n", "\n", "CONSISTENCY CHECK:\n", "------------------------------\n", "Feature types are consistent between training and test sets.\n"]}], "source": ["def analyze_feature_types(X_train, X_test):\n", "    \"\"\"\n", "    Analyze and display feature types for training and test sets\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"FEATURE TYPE ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Training set analysis\n", "    print(\"TRAINING SET:\")\n", "    print(\"-\" * 30)\n", "    train_categorical = X_train.select_dtypes(include=['object', 'category']).columns.tolist()\n", "    train_continuous = X_train.select_dtypes(include=[np.number]).columns.tolist()\n", "    \n", "    print(f\"Categorical features ({len(train_categorical)}): {train_categorical}\")\n", "    print(f\"Continuous features ({len(train_continuous)}): {train_continuous}\")\n", "    \n", "    # Test set analysis\n", "    print(\"\\nTEST SET:\")\n", "    print(\"-\" * 30)\n", "    test_categorical = X_test.select_dtypes(include=['object', 'category']).columns.tolist()\n", "    test_continuous = X_test.select_dtypes(include=[np.number]).columns.tolist()\n", "    \n", "    print(f\"Categorical features ({len(test_categorical)}): {test_categorical}\")\n", "    print(f\"Continuous features ({len(test_continuous)}): {test_continuous}\")\n", "    \n", "    # Verify consistency\n", "    print(\"\\nCONSISTENCY CHECK:\")\n", "    print(\"-\" * 30)\n", "    if set(train_categorical) == set(test_categorical) and set(train_continuous) == set(test_continuous):\n", "        print(\"Feature types are consistent between training and test sets.\")\n", "    else:\n", "        print(\"Warning: Feature types differ between training and test sets!\")\n", "    \n", "    return train_categorical, train_continuous\n", "\n", "# Analyze feature types\n", "if 'X_train' in locals() and 'X_test' in locals():\n", "    categorical_features, continuous_features = analyze_feature_types(X_train, X_test)"]}, {"cell_type": "markdown", "id": "d5ce05b2", "metadata": {}, "source": ["### 1d. Categorical Feature Encoding"]}, {"cell_type": "code", "execution_count": 13, "id": "6bc50852", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.219233Z", "iopub.status.busy": "2025-07-16T23:07:57.218264Z", "iopub.status.idle": "2025-07-16T23:07:57.226372Z", "shell.execute_reply": "2025-07-16T23:07:57.225963Z"}}, "outputs": [], "source": ["def encode_categorical_features(X_train, X_test, categorical_features):\n", "    \"\"\"\n", "    Encode categorical features using appropriate encoding methods\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"CATEGORICAL FEATURE ENCODING\")\n", "    print(\"=\" * 60)\n", "    \n", "    if not categorical_features:\n", "        print(\"No categorical features found for encoding.\")\n", "        return X_train.copy(), X_test.copy(), {}\n", "    \n", "    X_train_encoded = X_train.copy()\n", "    X_test_encoded = X_test.copy()\n", "    encoding_info = {}\n", "    \n", "    for feature in categorical_features:\n", "        unique_count = X_train[feature].nunique()\n", "        print(f\"\\nProcessing feature: {feature} ({unique_count} unique values)\")\n", "        \n", "        if unique_count == 2:\n", "            # Binary categorical feature - use Label Encoding\n", "            le = LabelEncoder()\n", "            X_train_encoded[feature] = le.fit_transform(X_train[feature])\n", "            X_test_encoded[feature] = le.transform(X_test[feature])\n", "            \n", "            encoding_info[feature] = {\n", "                'method': 'Label Encoding',\n", "                'classes': le.classes_,\n", "                'reason': 'Binary categorical feature'\n", "            }\n", "            print(f\"  Method: Label Encoding (Binary feature)\")\n", "            print(f\"  Mapping: {dict(zip(le.classes_, le.transform(le.classes_)))}\")\n", "        \n", "        else:\n", "            # Multi-class categorical feature - use One-Hot Encoding\n", "            # Get dummies for training set\n", "            train_dummies = pd.get_dummies(X_train[feature], prefix=feature, drop_first=True)\n", "            test_dummies = pd.get_dummies(X_test[feature], prefix=feature, drop_first=True)\n", "            \n", "            # Ensure test set has same columns as training set\n", "            for col in train_dummies.columns:\n", "                if col not in test_dummies.columns:\n", "                    test_dummies[col] = 0\n", "            \n", "            # Reorder columns to match training set\n", "            test_dummies = test_dummies[train_dummies.columns]\n", "            \n", "            # Drop original column and add dummy columns\n", "            X_train_encoded = X_train_encoded.drop(columns=[feature])\n", "            X_test_encoded = X_test_encoded.drop(columns=[feature])\n", "            \n", "            X_train_encoded = pd.concat([X_train_encoded, train_dummies], axis=1)\n", "            X_test_encoded = pd.concat([X_test_encoded, test_dummies], axis=1)\n", "            \n", "            encoding_info[feature] = {\n", "                'method': 'One-Hot Encoding',\n", "                'new_columns': train_dummies.columns.tolist(),\n", "                'reason': f'Multi-class categorical feature ({unique_count} classes)'\n", "            }\n", "            print(f\"  Method: One-Hot Encoding ({unique_count} classes)\")\n", "            print(f\"  New columns: {train_dummies.columns.tolist()}\")\n", "    \n", "    print(f\"\\nEncoding completed!\")\n", "    print(f\"Training set shape after encoding: {X_train_encoded.shape}\")\n", "    print(f\"Test set shape after encoding: {X_test_encoded.shape}\")\n", "    \n", "    return X_train_encoded, X_test_encoded, encoding_info"]}, {"cell_type": "code", "execution_count": 14, "id": "25c841a4", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.229413Z", "iopub.status.busy": "2025-07-16T23:07:57.228382Z", "iopub.status.idle": "2025-07-16T23:07:57.241040Z", "shell.execute_reply": "2025-07-16T23:07:57.241040Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "CATEGORICAL FEATURE ENCODING\n", "============================================================\n", "\n", "Processing feature: Drug (2 unique values)\n", "  Method: Label Encoding (Binary feature)\n", "  Mapping: {'D-penicillamine': 0, 'Placebo': 1}\n", "\n", "Processing feature: Sex (2 unique values)\n", "  Method: Label Encoding (Binary feature)\n", "  Mapping: {'F': 0, 'M': 1}\n", "\n", "Processing feature: Ascites (2 unique values)\n", "  Method: Label Encoding (Binary feature)\n", "  Mapping: {'N': 0, 'Y': 1}\n", "\n", "Processing feature: Hepatomegaly (2 unique values)\n", "  Method: Label Encoding (Binary feature)\n", "  Mapping: {'N': 0, 'Y': 1}\n", "\n", "Processing feature: Spider<PERSON> (2 unique values)\n", "  Method: Label Encoding (Binary feature)\n", "  Mapping: {'N': 0, 'Y': 1}\n", "\n", "Processing feature: Edema (3 unique values)\n", "  Method: One-Hot Encoding (3 classes)\n", "  New columns: ['<PERSON><PERSON>_<PERSON>', '<PERSON><PERSON>_Y']\n", "\n", "Encoding completed!\n", "Training set shape after encoding: (334, 20)\n", "Test set shape after encoding: (84, 20)\n", "\n", "Encoding Summary:\n", "\n", "Drug:\n", "  Method: Label Encoding\n", "  Reason: Binary categorical feature\n", "  Classes: ['D-penicillamine' 'Placebo']\n", "\n", "Sex:\n", "  Method: Label Encoding\n", "  Reason: Binary categorical feature\n", "  Classes: ['F' 'M']\n", "\n", "Ascites:\n", "  Method: Label Encoding\n", "  Reason: Binary categorical feature\n", "  Classes: ['N' 'Y']\n", "\n", "Hepatomegaly:\n", "  Method: Label Encoding\n", "  Reason: Binary categorical feature\n", "  Classes: ['N' 'Y']\n", "\n", "Spiders:\n", "  Method: Label Encoding\n", "  Reason: Binary categorical feature\n", "  Classes: ['N' 'Y']\n", "\n", "Edema:\n", "  Method: One-Hot Encoding\n", "  Reason: Multi-class categorical feature (3 classes)\n", "  New columns: ['<PERSON><PERSON>_<PERSON>', '<PERSON><PERSON>_Y']\n"]}], "source": ["# Apply categorical encoding\n", "if 'categorical_features' in locals() and 'X_train' in locals():\n", "    X_train_encoded, X_test_encoded, encoding_info = encode_categorical_features(X_train, X_test, categorical_features)\n", "    \n", "    # Display encoding summary\n", "    if encoding_info:\n", "        print(\"\\nEncoding Summary:\")\n", "        for feature, info in encoding_info.items():\n", "            print(f\"\\n{feature}:\")\n", "            print(f\"  Method: {info['method']}\")\n", "            print(f\"  Reason: {info['reason']}\")\n", "            if 'classes' in info:\n", "                print(f\"  Classes: {info['classes']}\")\n", "            if 'new_columns' in info:\n", "                print(f\"  New columns: {info['new_columns']}\")"]}, {"cell_type": "markdown", "id": "e51ba213", "metadata": {}, "source": ["### 1e. Label Distribution Analysis"]}, {"cell_type": "code", "execution_count": 15, "id": "fa9276f2", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.244044Z", "iopub.status.busy": "2025-07-16T23:07:57.244044Z", "iopub.status.idle": "2025-07-16T23:07:57.253563Z", "shell.execute_reply": "2025-07-16T23:07:57.253297Z"}}, "outputs": [], "source": ["def analyze_label_distribution(y_train, y_test, target_name=\"Target\"):\n", "    \"\"\"\n", "    Analyze and visualize label distribution in training and test sets\n", "    \"\"\"\n", "    print(\"=\" * 60)\n", "    print(\"LABEL DISTRIBUTION ANALYSIS\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Training set distribution\n", "    print(\"TRAINING SET DISTRIBUTION:\")\n", "    print(\"-\" * 30)\n", "    train_counts = y_train.value_counts().sort_index()\n", "    train_percentages = y_train.value_counts(normalize=True).sort_index() * 100\n", "    \n", "    for label in train_counts.index:\n", "        count = train_counts[label]\n", "        percentage = train_percentages[label]\n", "        print(f\"Class {label}: {count} samples ({percentage:.1f}%)\")\n", "    \n", "    # Test set distribution\n", "    print(\"\\nTEST SET DISTRIBUTION:\")\n", "    print(\"-\" * 30)\n", "    test_counts = y_test.value_counts().sort_index()\n", "    test_percentages = y_test.value_counts(normalize=True).sort_index() * 100\n", "    \n", "    for label in test_counts.index:\n", "        count = test_counts[label]\n", "        percentage = test_percentages[label]\n", "        print(f\"Class {label}: {count} samples ({percentage:.1f}%)\")\n", "    \n", "    # Balance analysis\n", "    print(\"\\nBALANCE ANALYSIS:\")\n", "    print(\"-\" * 30)\n", "    \n", "    # Calculate imbalance ratio\n", "    max_count = train_counts.max()\n", "    min_count = train_counts.min()\n", "    imbalance_ratio = max_count / min_count\n", "    \n", "    print(f\"Imbalance ratio: {imbalance_ratio:.2f}\")\n", "    \n", "    if imbalance_ratio <= 1.5:\n", "        balance_status = \"BALANCED\"\n", "    elif imbalance_ratio <= 3.0:\n", "        balance_status = \"MODERATELY IMBALANCED\"\n", "    else:\n", "        balance_status = \"HIGHLY IMBALANCED\"\n", "    \n", "    print(f\"Dataset balance status: {balance_status}\")\n", "    \n", "    # Survival status interpretation\n", "    print(\"\\nSURVIVAL STATUS INTERPRETATION:\")\n", "    print(\"-\" * 30)\n", "    print(\"0 = D (Death)\")\n", "    print(\"1 = C (Censored)\")\n", "    print(\"2 = CL (Censored due to liver transplantation)\")\n", "    \n", "    # Create visualization\n", "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))\n", "    \n", "    # Training set distribution\n", "    train_counts.plot(kind='bar', ax=ax1, color='skyblue', alpha=0.7)\n", "    ax1.set_title('Training Set Label Distribution')\n", "    ax1.set_xlabel('Survival Status')\n", "    ax1.set_ylabel('Count')\n", "    ax1.tick_params(axis='x', rotation=0)\n", "    \n", "    # Add percentage labels on bars\n", "    for i, (label, count) in enumerate(train_counts.items()):\n", "        percentage = train_percentages[label]\n", "        ax1.text(i, count + max_count*0.01, f'{percentage:.1f}%', \n", "                ha='center', va='bottom')\n", "    \n", "    # Test set distribution\n", "    test_counts.plot(kind='bar', ax=ax2, color='lightcoral', alpha=0.7)\n", "    ax2.set_title('Test Set Label Distribution')\n", "    ax2.set_xlabel('Survival Status')\n", "    ax2.set_ylabel('Count')\n", "    ax2.tick_params(axis='x', rotation=0)\n", "    \n", "    # Add percentage labels on bars\n", "    test_max = test_counts.max()\n", "    for i, (label, count) in enumerate(test_counts.items()):\n", "        percentage = test_percentages[label]\n", "        ax2.text(i, count + test_max*0.01, f'{percentage:.1f}%', \n", "                ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return balance_status, imbalance_ratio"]}, {"cell_type": "code", "execution_count": 16, "id": "5ee462a6", "metadata": {"execution": {"iopub.execute_input": "2025-07-16T23:07:57.255602Z", "iopub.status.busy": "2025-07-16T23:07:57.255602Z", "iopub.status.idle": "2025-07-16T23:07:57.595887Z", "shell.execute_reply": "2025-07-16T23:07:57.595887Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["============================================================\n", "LABEL DISTRIBUTION ANALYSIS\n", "============================================================\n", "TRAINING SET DISTRIBUTION:\n", "------------------------------\n", "Class C: 185 samples (55.4%)\n", "Class CL: 20 samples (6.0%)\n", "Class D: 129 samples (38.6%)\n", "\n", "TEST SET DISTRIBUTION:\n", "------------------------------\n", "Class C: 47 samples (56.0%)\n", "Class CL: 5 samples (6.0%)\n", "Class D: 32 samples (38.1%)\n", "\n", "BALANCE ANALYSIS:\n", "------------------------------\n", "Imbalance ratio: 9.25\n", "Dataset balance status: HIGHLY IMBALANCED\n", "\n", "SURVIVAL STATUS INTERPRETATION:\n", "------------------------------\n", "0 = D (Death)\n", "1 = C (Censored)\n", "2 = CL (Censored due to liver transplantation)\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "FINAL SUMMARY:\n", "============================================================\n", "Dataset balance: HIGHLY IMBALANCED\n", "Imbalance ratio: 9.25\n", "\n", "RECOMMENDATIONS:\n", "- Consider using stratified sampling\n", "- Apply class balancing techniques (SMOTE, class weights)\n", "- Use appropriate evaluation metrics (F1-score, AUC-ROC)\n", "- Consider ensemble methods that handle imbalanced data well\n"]}], "source": ["# Analyze label distribution\n", "if 'y_train' in locals() and 'y_test' in locals():\n", "    balance_status, imbalance_ratio = analyze_label_distribution(y_train, y_test, \"Survival Status\")\n", "    \n", "    print(f\"\\nFINAL SUMMARY:\")\n", "    print(\"=\" * 60)\n", "    print(f\"Dataset balance: {balance_status}\")\n", "    print(f\"Imbalance ratio: {imbalance_ratio:.2f}\")\n", "    \n", "    if balance_status != \"BALANCED\":\n", "        print(\"\\nRECOMMENDATIONS:\")\n", "        print(\"- Consider using stratified sampling\")\n", "        print(\"- Apply class balancing techniques (SMOTE, class weights)\")\n", "        print(\"- Use appropriate evaluation metrics (F1-score, AUC-ROC)\")\n", "        print(\"- Consider ensemble methods that handle imbalanced data well\")"]}], "metadata": {"language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}