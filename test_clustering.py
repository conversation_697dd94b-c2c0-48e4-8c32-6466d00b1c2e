#!/usr/bin/env python3
"""
Test script for clustering analysis with obesity dataset
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import <PERSON><PERSON>ncoder, StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import warnings
warnings.filterwarnings('ignore')

def load_data(local_file_path=None, url=None):
    """
    Load dataset from local file or remote URL
    """
    import os
    
    # Try loading from local file first if path is provided
    if local_file_path:
        print(f"Attempting to load dataset from local file: {local_file_path}")
        try:
            if not os.path.exists(local_file_path):
                print(f"Local file not found: {local_file_path}")
                return None
            else:
                df = pd.read_csv(local_file_path)
                print(f"Dataset loaded successfully from local file! Shape: {df.shape}")
                return df
        except Exception as e:
            print(f"Error reading local file: {e}")
            return None
    
    print("No local file path provided")
    return None

def main():
    print("=" * 60)
    print("CLUSTERING ANALYSIS TEST")
    print("=" * 60)
    
    # Load the obesity dataset
    df_obesity = load_data(local_file_path='C:/datasets/ObesityDataSet_raw_and_data_sinthetic.csv')
    
    if df_obesity is None:
        print('Failed to load obesity dataset. Creating sample data for testing...')
        # Create sample data for testing
        np.random.seed(42)
        df_obesity = pd.DataFrame({
            'Gender': np.random.choice(['Male', 'Female'], 100),
            'Age': np.random.randint(18, 65, 100),
            'Height': np.random.normal(1.7, 0.1, 100),
            'Weight': np.random.normal(70, 15, 100),
            'CALC': np.random.choice(['no', 'Sometimes', 'Frequently'], 100),
            'NObeyesdad': np.random.choice(['Normal_Weight', 'Overweight_Level_I', 'Obesity_Type_I'], 100)
        })
        print(f"Sample dataset created! Shape: {df_obesity.shape}")
    
    print(f"\nDataset Info:")
    print(f"Shape: {df_obesity.shape}")
    print(f"Columns: {list(df_obesity.columns)}")
    
    # Task 6: Remove class label
    print("\n" + "=" * 40)
    print("REMOVING CLASS LABEL")
    print("=" * 40)
    
    if 'NObeyesdad' in df_obesity.columns:
        print(f"Original shape: {df_obesity.shape}")
        df_obesity_no_label = df_obesity.drop(columns=['NObeyesdad'])
        print(f"Shape after removing class label: {df_obesity_no_label.shape}")
    else:
        df_obesity_no_label = df_obesity.copy()
        print("No NObeyesdad column found")
    
    # Task 7: Encode categorical features
    print("\n" + "=" * 40)
    print("ENCODING CATEGORICAL FEATURES")
    print("=" * 40)
    
    categorical_features = df_obesity_no_label.select_dtypes(include=['object']).columns.tolist()
    numerical_features = df_obesity_no_label.select_dtypes(include=[np.number]).columns.tolist()
    
    print(f"Categorical features: {categorical_features}")
    print(f"Numerical features: {numerical_features}")
    
    df_encoded = df_obesity_no_label.copy()
    
    for feature in categorical_features:
        unique_count = df_obesity_no_label[feature].nunique()
        print(f"\n{feature}: {unique_count} unique values")
        
        if unique_count == 2:
            # Binary - use Label Encoding
            le = LabelEncoder()
            df_encoded[feature] = le.fit_transform(df_obesity_no_label[feature])
            print(f"  Applied Label Encoding")
        else:
            # Multi-class - use One-Hot Encoding
            dummies = pd.get_dummies(df_obesity_no_label[feature], prefix=feature, drop_first=True)
            df_encoded = df_encoded.drop(columns=[feature])
            df_encoded = pd.concat([df_encoded, dummies], axis=1)
            print(f"  Applied One-Hot Encoding, created {len(dummies.columns)} new columns")
    
    print(f"\nFinal encoded dataset shape: {df_encoded.shape}")
    
    # Scale features
    print("\n" + "=" * 40)
    print("SCALING FEATURES")
    print("=" * 40)
    
    scaler = StandardScaler()
    df_scaled = pd.DataFrame(
        scaler.fit_transform(df_encoded),
        columns=df_encoded.columns,
        index=df_encoded.index
    )
    print(f"Scaled dataset shape: {df_scaled.shape}")
    
    # Task 8: Determine optimal k using Silhouette Score
    print("\n" + "=" * 40)
    print("DETERMINING OPTIMAL K")
    print("=" * 40)
    
    k_range = range(2, min(6, len(df_scaled)//2))  # Limit range for small datasets
    silhouette_scores = []
    
    for k in k_range:
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(df_scaled)
        score = silhouette_score(df_scaled, labels)
        silhouette_scores.append(score)
        print(f"k={k}: Silhouette Score = {score:.4f}")
    
    optimal_k = k_range[silhouette_scores.index(max(silhouette_scores))]
    print(f"\nOptimal k: {optimal_k}")
    print(f"Best Silhouette Score: {max(silhouette_scores):.4f}")
    
    # Task 9: Compare KMeans vs KMeans++
    print("\n" + "=" * 40)
    print("KMEANS VS KMEANS++ COMPARISON")
    print("=" * 40)
    
    # KMeans with random initialization
    kmeans_random = KMeans(n_clusters=optimal_k, init='random', random_state=42, n_init=10)
    labels_random = kmeans_random.fit_predict(df_scaled)
    silhouette_random = silhouette_score(df_scaled, labels_random)
    
    # KMeans++
    kmeans_plus = KMeans(n_clusters=optimal_k, init='k-means++', random_state=42, n_init=10)
    labels_plus = kmeans_plus.fit_predict(df_scaled)
    silhouette_plus = silhouette_score(df_scaled, labels_plus)
    
    print(f"\nKMeans (Random Init):")
    print(f"  Silhouette Score: {silhouette_random:.4f}")
    print(f"  Inertia: {kmeans_random.inertia_:.2f}")
    print(f"  Iterations: {kmeans_random.n_iter_}")
    
    print(f"\nKMeans++:")
    print(f"  Silhouette Score: {silhouette_plus:.4f}")
    print(f"  Inertia: {kmeans_plus.inertia_:.2f}")
    print(f"  Iterations: {kmeans_plus.n_iter_}")
    
    # Conclusion
    print("\n" + "=" * 40)
    print("CONCLUSION")
    print("=" * 40)
    
    if silhouette_plus >= silhouette_random:
        print("✓ KMeans++ performed better or equal to random initialization")
    else:
        print("✓ KMeans with random initialization performed better on this run")
    
    print(f"✓ Successfully clustered {len(df_scaled)} samples into {optimal_k} clusters")
    print("✓ All clustering analysis tasks completed successfully!")

if __name__ == "__main__":
    main()
